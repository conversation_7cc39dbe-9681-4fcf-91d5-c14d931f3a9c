# IID合规性数据生成报告

## 概述
此文档记录了数据集的IID (Independent and Identically Distributed) 合规性验证结果。

## IID合规性的重要性
机器学习的核心假设是训练集、验证集和测试集应该来自同一分布。只有这样：
- 验证集才能有效指导训练过程
- 测试集才能真实反映模型在未知数据上的泛化能力
- 超参数调优才能产生可靠的结果

## 数据生成流程

### 统一生成策略
1. **统一批次生成**: 在单一循环中生成所有批次，确保全局随机性
2. **随机打乱**: 使用 `random.shuffle()` 打乱所有批次
3. **按比例划分**: 将打乱后的批次按固定比例分配到三个数据集
4. **文件重组**: 移动到最终目录结构并重新编号

### 配置参数
- **num_batches**: 100
- **num_validation_batches**: 20
- **num_test_batches**: 1
- **burst_mode_enabled**: False
- **burst_batch_ratio**: 0
- **flows_per_batch_range**: (0, 0)
- **congestion_ratio_range**: (0, 0)

## 验证结果

### 整体统计
- **总批次数**: 121
- **总流数**: 2848
- **总爆发批次数**: 44
- **平均爆发比例**: 36.4%
- **分布方差**: 0.028867
- **IID合规性**: ❌ 失败

### 各数据集详细统计

#### TRAIN 数据集
- 总批次数: 100
- 爆发批次数: 37
- 爆发比例: 37.00%
- 总流数: 2336
- 聚集流数: 1385

#### VALIDATION 数据集
- 总批次数: 20
- 爆发批次数: 7
- 爆发比例: 35.00%
- 总流数: 480
- 聚集流数: 291

#### TEST 数据集
- 总批次数: 1
- 爆发批次数: 0
- 爆发比例: 0.00%
- 总流数: 32
- 聚集流数: 18

### 分布一致性分析

各数据集爆发比例对比:
- train: 37.00%
- validation: 35.00%
- test: 0.00%

方差分析: 0.028867

⚠️ **结论**: 数据集不满足IID假设，建议重新生成数据或调整参数。

## 技术说明

### IID合规性判断标准
- **方差阈值**: 0.01 (各数据集爆发比例方差应小于1%)
- **随机性**: 使用全局随机种子确保可重现性
- **划分策略**: 先生成后划分，避免分别生成导致的分布差异

### 文件结构
```
datasets/
├── train/
│   ├── batch_0.json
│   ├── batch_1.json
│   └── ...
├── validation/
│   ├── batch_0.json
│   ├── batch_1.json
│   └── ...
└── test/
    ├── batch_0.json
    ├── batch_1.json
    └── ...
```

---
*此报告由IID合规性数据生成器自动生成*
