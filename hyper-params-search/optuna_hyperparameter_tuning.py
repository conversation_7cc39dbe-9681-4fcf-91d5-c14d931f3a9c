#!/usr/bin/env python3
"""
使用Optuna进行超参数调优的脚本
支持Web可视化界面和分布式优化
"""

import optuna
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import json
import os
import logging
import argparse
import time
from datetime import datetime
from tqdm import tqdm
import sqlite3

# 导入现有模块
from train import (
    STGNNModel, SimulationDataset, FeatureAssembler,
    run_one_epoch, evaluate, setup_logging,
    DEVICE, SPATIAL_EMBEDDING_DIM, FUTURE_EMBEDDING_DIM
)

# ==================== 全局配置 ====================
STUDY_NAME = "stgnn_hyperparameter_optimization"
STORAGE_URL = "sqlite:///optuna_study.db"
N_TRIALS = 50
TIMEOUT = 24 * 3600  # 24小时超时
EPOCHS_PER_TRIAL = 35  # 每个trial训练的轮数，相比完整训练减少以加速搜索
FILE_BATCH_SIZE = 1

def create_trial_directory(trial_number: int) -> str:
    """为每个trial创建独立的目录"""
    trial_dir = f"trial_{trial_number:04d}"
    os.makedirs(trial_dir, exist_ok=True)
    return trial_dir

def objective(trial: optuna.Trial) -> float:
    """
    Optuna优化目标函数
    
    Args:
        trial: Optuna试验对象
        
    Returns:
        验证损失值（越小越好）
    """
    
    # ================== 1. 定义超参数搜索空间 ==================
    # 学习率：在对数均匀分布中选择
    lr = trial.suggest_float('lr', 1e-5, 1e-2, log=True)
    
    # 隐藏层维度 (减少最大值以节省内存)
    hidden_dim = trial.suggest_categorical('hidden_dim', [64, 128, 256])
    
    # GNN层数 (减少最大层数)
    num_gnn_layers = trial.suggest_int('num_gnn_layers', 2, 4)
    
    # Dropout率
    dropout_rate = trial.suggest_float('dropout_rate', 0.1, 0.4)
    
    # 权重衰减
    weight_decay = trial.suggest_float('weight_decay', 1e-5, 1e-3, log=True)
    
    # 优化器类型
    optimizer_name = trial.suggest_categorical("optimizer", ["AdamW", "Adam"])
    
    # 调度器类型 - 注释掉，统一使用ReduceLROnPlateau
    # scheduler_name = trial.suggest_categorical("scheduler", ["CosineAnnealingLR", "StepLR"])
    
    # Huber Loss的delta参数 - 注释掉，改用MAE
    # huber_delta = trial.suggest_float('huber_delta', 0.1, 0.3)
    
    # 空间和未来嵌入维度 (减少最大值)
    spatial_embedding_dim = trial.suggest_categorical('spatial_embedding_dim', [64, 128])
    future_embedding_dim = trial.suggest_categorical('future_embedding_dim', [32, 64])
    
    # LSTM相关参数 (减少最大值)
    lstm_hidden_dim = trial.suggest_categorical('lstm_hidden_dim', [64, 128])
    lstm_num_layers = trial.suggest_int('lstm_num_layers', 1, 2)
    
    # ================== 2. 创建trial目录和日志 ==================
    trial_dir = create_trial_directory(trial.number)
    trial_log_path = os.path.join(trial_dir, f"trial_{trial.number}.log")
    
    # 设置trial专用的日志记录
    trial_logger = logging.getLogger(f"trial_{trial.number}")
    trial_logger.setLevel(logging.INFO)
    trial_handler = logging.FileHandler(trial_log_path, mode='w')
    trial_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    trial_handler.setFormatter(trial_formatter)
    trial_logger.addHandler(trial_handler)
    
    trial_logger.info(f"开始Trial {trial.number}")
    trial_logger.info(f"超参数: {trial.params}")
    
    try:
        # ================== 3. 清理GPU缓存 ==================
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
        
        # ================== 4. 加载数据和元数据 ==================
        processed_dir = "datasets_processed"
        metadata = torch.load(os.path.join(processed_dir, 'metadata.pt'))
        num_links = metadata['num_links']
        
        # ================== 4. 构建模型 ==================
        hyperparams = {
            'spatial_embedding_dim': spatial_embedding_dim,
            'future_embedding_dim': future_embedding_dim,
            'hidden_dim': hidden_dim,
            'num_gnn_layers': num_gnn_layers,
            'dropout_rate': dropout_rate,  # 需要修改模型以支持动态dropout
            'lstm_hidden_dim': lstm_hidden_dim,
            'lstm_num_layers': lstm_num_layers
        }
        
        # 临时修改全局DROPOUT_RATE
        import train
        original_dropout = train.DROPOUT_RATE
        train.DROPOUT_RATE = dropout_rate
        
        model = STGNNModel(num_links, **hyperparams).to(DEVICE)
        
        # ================== 5. 设置优化器和调度器 ==================
        if optimizer_name == "AdamW":
            optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
        elif optimizer_name == "Adam":
            optimizer = optim.Adam(model.parameters(), lr=lr, weight_decay=weight_decay)
        elif optimizer_name == "RMSprop":
            optimizer = optim.RMSprop(model.parameters(), lr=lr, weight_decay=weight_decay)
        
        # 注释掉原来的调度器选择逻辑
        # if scheduler_name == "CosineAnnealingLR":
        #     scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=EPOCHS_PER_TRIAL, eta_min=1e-7)
        # elif scheduler_name == "StepLR":
        #     step_size = max(1, EPOCHS_PER_TRIAL // 3)
        #     scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=step_size, gamma=0.5)
        # elif scheduler_name == "ExponentialLR":
        #     gamma = 0.95
        #     scheduler = optim.lr_scheduler.ExponentialLR(optimizer, gamma=gamma)
        
        # 使用ReduceLROnPlateau调度器
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.1, patience=5,    
                                        verbose=False, threshold=0.001, threshold_mode='rel', cooldown=0, min_lr=2e-6, eps=1e-08)
        
        # ================== 6. 设置损失函数 ==================
        # 使用MAE (L1Loss) 替代HuberLoss
        # loss_fn = nn.HuberLoss(delta=huber_delta)  # 注释掉原有的HuberLoss
        loss_fn = nn.L1Loss()  # 使用MAE作为损失函数
        
        # ================== 7. 准备数据加载器 ==================
        feature_assembler = FeatureAssembler(num_links, metadata)
        
        # 使用较小的数据集进行快速验证
        train_dataset = SimulationDataset(data_dir=os.path.join(processed_dir, 'train'))
        train_loader = DataLoader(train_dataset, batch_size=FILE_BATCH_SIZE, shuffle=True, num_workers=8, pin_memory=True)
        
        val_dataset = SimulationDataset(data_dir=os.path.join(processed_dir, 'validation'))
        val_loader = DataLoader(val_dataset, batch_size=FILE_BATCH_SIZE, shuffle=False, num_workers=8)
        
        # ================== 8. 训练循环 ==================
        best_val_loss = float('inf')
        patience = 10  # 早停耐心
        patience_counter = 0
        
        for epoch in range(EPOCHS_PER_TRIAL):
            # 训练一个epoch
            avg_train_loss = run_one_epoch(model, train_loader, optimizer, loss_fn, feature_assembler)
            
            # 验证 - 计算详细指标
            avg_val_loss, val_metrics = evaluate(model, val_loader, loss_fn, feature_assembler, compute_detailed_metrics=True)
            
            # 更新调度器 - ReduceLROnPlateau需要传入验证损失
            scheduler.step(avg_val_loss)
            
            # 记录详细指标（仅在前几个epoch）
            if epoch < 3 or epoch % 5 == 0:
                print(f"Trial {trial.number} Epoch {epoch+1} - MAE: {val_metrics['mae']:.4f}, RMSE: {val_metrics['rmse']:.4f}, MAPE: {val_metrics['mape']:.2f}%")
            
            # 记录进度
            if epoch % 10 == 0:
                trial_logger.info(f"Epoch {epoch+1}/{EPOCHS_PER_TRIAL} | "
                                f"训练损失: {avg_train_loss:.6f} | "
                                f"验证损失: {avg_val_loss:.6f} | "
                                f"学习率: {optimizer.param_groups[0]['lr']:.6f}")
            
            # 更新最佳验证损失
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                patience_counter = 0
                
                # 保存最佳模型
                best_model_path = os.path.join(trial_dir, "best_model.pth")
                checkpoint = {
                    'model_state_dict': model.state_dict(),
                    'hyperparams': hyperparams,
                    'trial_params': trial.params,
                    'best_val_loss': best_val_loss,
                    'epoch': epoch
                }
                torch.save(checkpoint, best_model_path)
            else:
                patience_counter += 1
            
            # ================== 9. Optuna剪枝机制 ==================
            # 向Optuna报告当前epoch的验证损失
            trial.report(avg_val_loss, epoch)
            
            # 检查是否需要剪枝（提前终止不promising的trial）
            if trial.should_prune():
                trial_logger.info(f"Trial在epoch {epoch+1}被剪枝")
                raise optuna.exceptions.TrialPruned()
            
            # 早停机制
            if patience_counter >= patience:
                trial_logger.info(f"早停触发，在epoch {epoch+1}停止训练")
                break
        
        # ================== 10. 记录最终结果 ==================
        trial_logger.info(f"Trial {trial.number}完成，最佳验证损失: {best_val_loss:.6f}")
        
        # 保存trial结果
        result_data = {
            'trial_number': trial.number,
            'hyperparams': trial.params,
            'best_val_loss': best_val_loss,
            'final_epoch': epoch + 1,
            'model_path': os.path.join(trial_dir, "best_model.pth")
        }
        
        result_path = os.path.join(trial_dir, "trial_result.json")
        with open(result_path, 'w') as f:
            json.dump(result_data, f, indent=4)
        
        # 恢复原始DROPOUT_RATE
        train.DROPOUT_RATE = original_dropout
        
        return best_val_loss
        
    except torch.cuda.OutOfMemoryError as e:
        trial_logger.error(f"Trial {trial.number}内存不足: {str(e)}")
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
        # 恢复原始DROPOUT_RATE
        if 'original_dropout' in locals():
            train.DROPOUT_RATE = original_dropout
        # 对于内存不足，返回一个很大的损失值而不是抛出异常
        return float('inf')
        
    except Exception as e:
        trial_logger.error(f"Trial {trial.number}出现错误: {str(e)}")
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
        # 恢复原始DROPOUT_RATE
        if 'original_dropout' in locals():
            train.DROPOUT_RATE = original_dropout
        # 对于其他错误，也返回一个很大的损失值
        return float('inf')
    
    finally:
        # 清理日志handler
        trial_logger.removeHandler(trial_handler)
        trial_handler.close()

def main():
    """主函数：设置并运行Optuna优化"""
    
    # 声明全局变量
    global EPOCHS_PER_TRIAL, N_TRIALS, TIMEOUT
    
    parser = argparse.ArgumentParser(description='使用Optuna进行STGNN超参数调优')
    parser.add_argument('--n_trials', type=int, default=50, help='试验次数')
    parser.add_argument('--timeout', type=int, default=TIMEOUT, help='超时时间（秒）')
    parser.add_argument('--epochs_per_trial', type=int, default=35, help='每个trial的训练轮数')
    parser.add_argument('--study_name', type=str, default=STUDY_NAME, help='Study名称')
    parser.add_argument('--storage_url', type=str, default=STORAGE_URL, help='数据库存储URL')
    parser.add_argument('--resume', action='store_true', help='是否从现有study恢复')
    parser.add_argument('--prune', action='store_true', help='是否启用剪枝')
    parser.add_argument('--force_cpu', default=False, help='强制使用CPU训练（避免GPU内存问题）')
    parser.add_argument('--clear_gpu_cache', action='store_true', help='在开始前清理GPU缓存')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging()
    logging.info(f"开始Optuna超参数调优")
    logging.info(f"参数: {args}")
    
    # 更新全局变量
    EPOCHS_PER_TRIAL = args.epochs_per_trial
    N_TRIALS = args.n_trials
    TIMEOUT = args.timeout
    
    # 处理GPU相关设置
    if args.force_cpu:
        logging.info("强制使用CPU训练")
        global DEVICE
        DEVICE = torch.device('cpu')
        import train
        train.DEVICE = torch.device('cpu')
    
    if args.clear_gpu_cache and torch.cuda.is_available():
        logging.info("清理GPU缓存...")
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
        logging.info("GPU缓存清理完成")
    
    # ================== 创建或加载Study ==================
    if args.prune:
        pruner = optuna.pruners.MedianPruner(
            n_startup_trials=5,  # 前5个trial不进行剪枝
            n_warmup_steps=10,   # 前10个epoch不进行剪枝
            interval_steps=5     # 每5个epoch检查一次剪枝
        )
    else:
        pruner = optuna.pruners.NopPruner()
    
    if args.resume:
        # 从现有study恢复
        try:
            study = optuna.load_study(
                study_name=args.study_name,
                storage=args.storage_url,
                pruner=pruner
            )
            logging.info(f"从现有study恢复，已完成{len(study.trials)}个trials")
        except KeyError:
            logging.info("未找到现有study，创建新study")
            study = optuna.create_study(
                study_name=args.study_name,
                direction='minimize',
                storage=args.storage_url,
                pruner=pruner,
                load_if_exists=True
            )
    else:
        # 创建新study
        study = optuna.create_study(
            study_name=args.study_name,
            direction='minimize',
            storage=args.storage_url,
            pruner=pruner,
            load_if_exists=True
        )
        logging.info("创建新的Optuna study")
    
    # ================== 开始优化 ==================
    logging.info(f"开始优化，目标: {args.n_trials}个trials，超时: {args.timeout}秒")
    
    start_time = time.time()
    
    try:
        study.optimize(
            objective, 
            n_trials=args.n_trials,
            timeout=args.timeout,
            callbacks=[
                lambda study, trial: logging.info(
                    f"Trial {trial.number}完成，值: {trial.value:.6f}，"
                    f"最佳值: {study.best_value:.6f}"
                )
            ]
        )
    except KeyboardInterrupt:
        logging.info("优化被用户中断")
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    # ================== 输出结果 ==================
    logging.info("\n" + "="*50)
    logging.info("优化完成！")
    logging.info(f"总耗时: {elapsed_time/3600:.2f}小时")
    logging.info(f"完成的试验数: {len(study.trials)}")
    logging.info(f"最佳试验:")
    
    best_trial = study.best_trial
    logging.info(f"  试验编号: {best_trial.number}")
    logging.info(f"  验证损失: {best_trial.value:.6f}")
    logging.info(f"  超参数:")
    for key, value in best_trial.params.items():
        logging.info(f"    {key}: {value}")
    
    # ================== 保存最终结果 ==================
    final_results = {
        'study_name': args.study_name,
        'total_trials': len(study.trials),
        'best_trial_number': best_trial.number,
        'best_value': best_trial.value,
        'best_params': best_trial.params,
        'optimization_time_hours': elapsed_time / 3600,
        'timestamp': datetime.now().isoformat()
    }
    
    with open('optuna_final_results.json', 'w') as f:
        json.dump(final_results, f, indent=4)
    
    logging.info("最终结果已保存到 optuna_final_results.json")
    
    # ================== 生成优化历史图表 ==================
    try:
        import matplotlib.pyplot as plt
        
        # 优化历史
        plt.figure(figsize=(12, 4))
        
        plt.subplot(1, 2, 1)
        plt.plot([t.value for t in study.trials if t.value is not None])
        plt.xlabel('Trial')
        plt.ylabel('val loss')
        plt.title('optimization history')
        plt.grid(True)
        
        plt.subplot(1, 2, 2)
        best_values = []
        best_value = float('inf')
        for trial in study.trials:
            if trial.value is not None and trial.value < best_value:
                best_value = trial.value
            best_values.append(best_value if best_value != float('inf') else None)
        
        plt.plot(best_values)
        plt.xlabel('Trial')
        plt.ylabel('best val loss')
        plt.title('best value history')
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('optuna_optimization_history.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        logging.info("save to optuna_optimization_history.png")
        
    except ImportError:
        logging.warning("未安装matplotlib，跳过图表生成")
    
    # ================== 启动Web界面提示 ==================
    logging.info("\n" + "="*50)
    logging.info("要启动Web可视化界面，请运行:")
    logging.info(f"python optuna_dashboard.py --storage {args.storage_url}")
    logging.info("或者使用命令行:")
    logging.info(f"optuna-dashboard {args.storage_url}")
    logging.info("然后在浏览器中访问 http://localhost:8080")

if __name__ == "__main__":
    main() 