2025-06-29 23:45:25,288 - trial_4 - INFO - 开始Trial 4
2025-06-29 23:45:25,288 - trial_4 - INFO - 超参数: {'lr': 0.0003876389932527888, 'hidden_dim': 256, 'num_gnn_layers': 4, 'dropout_rate': 0.12059558851873409, 'weight_decay': 0.00026286520616730955, 'optimizer': 'AdamW', 'scheduler': 'StepLR', 'huber_delta': 0.29950033839303447, 'spatial_embedding_dim': 64, 'future_embedding_dim': 64, 'lstm_hidden_dim': 64, 'lstm_num_layers': 1}
2025-06-29 23:45:26,720 - trial_4 - ERROR - Trial 4内存不足: CUDA out of memory. Tried to allocate 2.98 GiB. GPU 0 has a total capacity of 23.64 GiB of which 2.84 GiB is free. Including non-PyTorch memory, this process has 20.39 GiB memory in use. Of the allocated memory 18.53 GiB is allocated by PyTorch, and 1.40 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
