# Optuna超参数优化使用指南

本项目使用Optuna框架对STGNN模型进行自动化超参数调优，并提供了Web可视化界面来分析优化结果。

## 📋 目录

- [快速开始](#快速开始)
- [文件说明](#文件说明)
- [安装依赖](#安装依赖)
- [使用方法](#使用方法)
- [Web可视化](#web可视化)
- [高级用法](#高级用法)
- [故障排除](#故障排除)

## 🚀 快速开始

### 1. 一键启动（推荐）

```bash
# 快速测试模式（10个trials，适合初次尝试）
python run_optuna_optimization.py --quick

# 完整优化模式（100个trials，生产环境使用）
python run_optuna_optimization.py --full

# 默认模式（50个trials，平衡速度与效果）
python run_optuna_optimization.py
```

### 2. 手动启动

```bash
# 1. 安装依赖
pip install -r requirements_optuna.txt

# 2. 运行超参数优化
python optuna_hyperparameter_tuning.py --n_trials 50 --prune

# 3. 启动Web可视化界面
python optuna_dashboard.py
```

## 📁 文件说明

| 文件名 | 功能 |
|--------|------|
| `optuna_hyperparameter_tuning.py` | 主要的超参数优化脚本 |
| `optuna_dashboard.py` | Web可视化界面启动脚本 |
| `run_optuna_optimization.py` | 一键启动脚本（推荐使用） |
| `requirements_optuna.txt` | Optuna相关依赖文件 |
| `README_optuna.md` | 本使用说明文档 |

## 📦 安装依赖

### 方法1：自动安装（推荐）
运行一键启动脚本时会自动检查并安装依赖：
```bash
python run_optuna_optimization.py
```

### 方法2：手动安装
```bash
pip install -r requirements_optuna.txt
```

### 核心依赖
- `optuna>=3.4.0` - 超参数优化框架
- `optuna-dashboard>=0.15.0` - Web可视化界面
- `matplotlib>=3.5.0` - 图表生成
- `pandas>=1.3.0` - 数据处理

## 🎛️ 使用方法

### 基本用法

#### 1. 快速模式（适合测试）
```bash
python run_optuna_optimization.py --quick
```
- 10个trials
- 每个trial训练10个epochs
- 2小时超时
- 快速验证优化流程

#### 2. 完整模式（适合生产）
```bash
python run_optuna_optimization.py --full
```
- 100个trials
- 每个trial训练50个epochs  
- 24小时超时
- 充分探索超参数空间

#### 3. 自定义参数
```bash
python optuna_hyperparameter_tuning.py \
    --n_trials 80 \
    --epochs_per_trial 40 \
    --timeout 18000 \
    --prune
```

### 可调优的超参数

| 超参数 | 搜索范围 | 说明 |
|--------|----------|------|
| `lr` | 1e-5 ~ 1e-2 | 学习率（对数分布） |
| `hidden_dim` | [64,128,256,512] | 隐藏层维度 |
| `num_gnn_layers` | 2~5 | GNN层数 |
| `dropout_rate` | 0.0~0.5 | Dropout率 |
| `weight_decay` | 1e-6~1e-3 | 权重衰减（对数分布） |
| `optimizer` | [AdamW,Adam,RMSprop] | 优化器类型 |
| `scheduler` | [CosineAnnealingLR,StepLR,ExponentialLR] | 学习率调度器 |
| `huber_delta` | 0.05~0.5 | Huber损失的delta参数 |
| `spatial_embedding_dim` | [64,128,256] | 空间嵌入维度 |
| `future_embedding_dim` | [32,64,128] | 未来嵌入维度 |
| `lstm_hidden_dim` | [64,128,256] | LSTM隐藏层维度 |
| `lstm_num_layers` | 1~3 | LSTM层数 |

### 恢复中断的优化

```bash
# 从现有study恢复优化
python optuna_hyperparameter_tuning.py --resume --n_trials 50
```

## 🌐 Web可视化

### 启动方法

#### 方法1：自动启动
优化完成后会提示是否启动Web界面，选择`y`即可。

#### 方法2：手动启动
```bash
# 使用默认设置
python optuna_dashboard.py

# 自定义端口和数据库
python optuna_dashboard.py --port 9090 --storage sqlite:///my_study.db

# 列出所有studies
python optuna_dashboard.py --list-studies
```

#### 方法3：命令行启动
```bash
optuna-dashboard sqlite:///optuna_study.db
```

### Web界面功能

访问 `http://localhost:8080` 可以查看：

1. **优化历史** - 显示每个trial的验证损失变化
2. **超参数重要性** - 分析各超参数对结果的影响
3. **超参数关系** - 超参数之间的相关性分析
4. **平行坐标图** - 多维超参数可视化
5. **试验详情** - 每个trial的详细信息

## 🔧 高级用法

### 分布式优化

```bash
# 在多台机器上同时运行（共享数据库）
# 机器1
python optuna_hyperparameter_tuning.py --storage mysql://user:pass@host/db

# 机器2
python optuna_hyperparameter_tuning.py --storage mysql://user:pass@host/db --resume
```

### 自定义剪枝策略

```python
# 修改 optuna_hyperparameter_tuning.py 中的剪枝器设置
pruner = optuna.pruners.MedianPruner(
    n_startup_trials=10,    # 前10个trial不剪枝
    n_warmup_steps=20,      # 前20个epoch不剪枝
    interval_steps=5        # 每5个epoch检查一次
)
```

### 多目标优化

```python
# 可以扩展objective函数返回多个目标
def objective(trial):
    # ... 训练代码 ...
    return [val_loss, model_size, training_time]  # 多目标

study = optuna.create_study(directions=['minimize', 'minimize', 'minimize'])
```

## 📊 结果分析

### 优化结果文件

- `optuna_final_results.json` - 最终优化结果汇总
- `optuna_optimization_history.png` - 优化历史图表
- `optuna_study.db` - SQLite数据库（所有trial数据）
- `trial_XXXX/` - 每个trial的详细结果

### 查看最佳结果

```python
import optuna
import json

# 从数据库加载study
study = optuna.load_study(
    study_name="stgnn_hyperparameter_optimization",
    storage="sqlite:///optuna_study.db"
)

# 打印最佳结果
print(f"最佳验证损失: {study.best_value}")
print(f"最佳超参数: {study.best_params}")

# 或者直接查看JSON文件
with open('optuna_final_results.json', 'r') as f:
    results = json.load(f)
    print(json.dumps(results, indent=2, ensure_ascii=False))
```

### 使用最佳超参数重新训练

```python
# 加载最佳超参数
best_params = study.best_params

# 用最佳超参数训练完整模型
python train.py \
    --lr {best_params['lr']} \
    --epochs 200 \
    --model_save_path best_optimized_model.pth
```

## 🛠️ 故障排除

### 常见问题

#### 1. 数据文件未找到
```
错误: 未找到预处理数据目录 datasets_processed
```
**解决方案**: 先运行数据预处理脚本生成数据。

#### 2. 依赖安装失败
```
错误: optuna-dashboard未安装
```
**解决方案**: 
```bash
pip install optuna-dashboard
# 或者
pip install -r requirements_optuna.txt
```

#### 3. 端口被占用
```
错误: 端口 8080 不可用
```
**解决方案**: 
```bash
python optuna_dashboard.py --port 9090
```

#### 4. 内存不足
```
CUDA out of memory
```
**解决方案**: 
- 减少batch size
- 减少模型层数
- 使用CPU训练

#### 5. Study不存在
```
KeyError: No study found
```
**解决方案**: 
```bash
# 不使用--resume参数，创建新study
python optuna_hyperparameter_tuning.py --n_trials 50
```

### 性能优化建议

1. **使用GPU加速**
   ```bash
   export CUDA_VISIBLE_DEVICES=0
   python optuna_hyperparameter_tuning.py
   ```

2. **启用剪枝以节省时间**
   ```bash
   python optuna_hyperparameter_tuning.py --prune
   ```

3. **调整epochs_per_trial平衡速度与精度**
   ```bash
   # 快速探索
   python optuna_hyperparameter_tuning.py --epochs_per_trial 20
   
   # 精确优化
   python optuna_hyperparameter_tuning.py --epochs_per_trial 100
   ```

4. **使用并行优化**
   ```bash
   # 终端1
   python optuna_hyperparameter_tuning.py --resume &
   
   # 终端2  
   python optuna_hyperparameter_tuning.py --resume &
   ```

### 日志和调试

- 主日志: `training.log`
- Trial日志: `trial_XXXX/trial_XXXX.log`
- 启用详细日志: 在脚本中设置 `logging.DEBUG`

## 📞 获取帮助

如果遇到问题：

1. 查看日志文件获取详细错误信息
2. 检查数据文件是否存在
3. 验证依赖是否正确安装
4. 查看本README的故障排除部分

---

🎯 **开始优化您的模型吧！**

```bash
python run_optuna_optimization.py --quick
``` 