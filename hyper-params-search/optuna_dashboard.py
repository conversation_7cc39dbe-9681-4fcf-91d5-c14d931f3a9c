#!/usr/bin/env python3
"""
Optuna Web可视化界面启动脚本
提供友好的Web界面来查看和分析超参数优化结果
"""

import argparse
import os
import sys
import subprocess
import json
import logging
from datetime import datetime
import webbrowser
import time
import socket

def check_port_available(port: int) -> bool:
    """检查端口是否可用"""
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('localhost', port))
    sock.close()
    return result != 0

def find_available_port(start_port: int = 8080) -> int:
    """找到可用的端口"""
    port = start_port
    while port < start_port + 100:  # 最多尝试100个端口
        if check_port_available(port):
            return port
        port += 1
    raise RuntimeError(f"无法找到可用端口，从{start_port}开始的100个端口都被占用")

def get_study_info(storage_url: str, study_name: str = None) -> dict:
    """获取study的基本信息"""
    try:
        import optuna
        
        if study_name:
            study = optuna.load_study(study_name=study_name, storage=storage_url)
        else:
            # 如果没有指定study名称，尝试加载默认的study
            study = optuna.load_study(storage=storage_url)
        
        info = {
            'study_name': study.study_name,
            'direction': study.direction.name,
            'n_trials': len(study.trials),
            'best_value': study.best_value if study.best_trial else None,
            'best_params': study.best_params if study.best_trial else None,
            'study_id': study._study_id if hasattr(study, '_study_id') else None
        }
        
        return info
        
    except Exception as e:
        print(f"获取study信息时出错: {e}")
        return {}

def start_dashboard(storage_url: str, port: int = 8080, host: str = "0.0.0.0", 
                   study_name: str = None, auto_open: bool = True) -> None:
    """启动Optuna Dashboard"""
    
    # 检查optuna-dashboard是否已安装
    try:
        subprocess.run(["optuna-dashboard", "--version"], 
                      capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("错误: optuna-dashboard未安装")
        print("请运行以下命令安装:")
        print("pip install optuna-dashboard")
        return
    
    # 检查数据库文件是否存在
    if storage_url.startswith("sqlite:///"):
        db_file = storage_url.replace("sqlite:///", "")
        if not os.path.exists(db_file):
            print(f"错误: 数据库文件 {db_file} 不存在")
            print("请先运行超参数优化脚本生成数据")
            return
    
    # 获取study信息
    study_info = get_study_info(storage_url, study_name)
    if study_info:
        print("Study信息:")
        for key, value in study_info.items():
            print(f"  {key}: {value}")
        print()
    
    # 查找可用端口
    try:
        available_port = find_available_port(port)
        if available_port != port:
            print(f"端口 {port} 不可用，使用端口 {available_port}")
            port = available_port
    except RuntimeError as e:
        print(f"错误: {e}")
        return
    
    # 构建命令
    cmd = [
        "optuna-dashboard",
        storage_url,
        "--host", host,
        "--port", str(port)
    ]
    
    print(f"启动Optuna Dashboard...")
    print(f"存储: {storage_url}")
    print(f"地址: http://{host}:{port}")
    print(f"命令: {' '.join(cmd)}")
    print()
    
    # 自动打开浏览器
    if auto_open:
        def open_browser():
            time.sleep(2)  # 等待服务器启动
            webbrowser.open(f"http://localhost:{port}")
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        print("正在自动打开浏览器...")
    
    print("按 Ctrl+C 停止服务器")
    print("="*50)
    
    try:
        # 启动dashboard
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except subprocess.CalledProcessError as e:
        print(f"启动dashboard失败: {e}")

def main():
    parser = argparse.ArgumentParser(
        description='启动Optuna Web可视化界面',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python optuna_dashboard.py                                    # 使用默认设置
  python optuna_dashboard.py --port 9090                       # 指定端口
  python optuna_dashboard.py --storage sqlite:///my_study.db   # 指定数据库
  python optuna_dashboard.py --study-name my_study             # 指定study名称
  python optuna_dashboard.py --host 127.0.0.1 --no-auto-open  # 本地访问，不自动开浏览器
        """
    )
    
    parser.add_argument(
        '--storage', 
        type=str, 
        default='sqlite:///optuna_study.db',
        help='Optuna存储URL (默认: sqlite:///optuna_study.db)'
    )
    
    parser.add_argument(
        '--port', 
        type=int, 
        default=8080,
        help='Web服务器端口 (默认: 8080)'
    )
    
    parser.add_argument(
        '--host', 
        type=str, 
        default='0.0.0.0',
        help='Web服务器主机地址 (默认: 0.0.0.0, 所有接口)'
    )
    
    parser.add_argument(
        '--study-name', 
        type=str,
        help='要显示的study名称 (可选)'
    )
    
    parser.add_argument(
        '--no-auto-open', 
        action='store_true',
        help='不自动打开浏览器'
    )
    
    parser.add_argument(
        '--list-studies', 
        action='store_true',
        help='列出数据库中的所有studies'
    )
    
    args = parser.parse_args()
    
    # 如果要求列出studies
    if args.list_studies:
        try:
            import optuna
            studies = optuna.get_all_study_summaries(storage=args.storage)
            if studies:
                print("数据库中的Studies:")
                for study in studies:
                    print(f"  - {study.study_name} (方向: {study.direction.name}, "
                          f"试验数: {study.n_trials})")
            else:
                print("数据库中没有找到studies")
        except Exception as e:
            print(f"列出studies时出错: {e}")
        return
    
    # 启动dashboard
    start_dashboard(
        storage_url=args.storage,
        port=args.port,
        host=args.host,
        study_name=args.study_name,
        auto_open=not args.no_auto_open
    )

if __name__ == "__main__":
    main() 