2025-06-30 01:22:15,705 - trial_39 - INFO - 开始Trial 39
2025-06-30 01:22:15,705 - trial_39 - INFO - 超参数: {'lr': 0.00026568171965807075, 'hidden_dim': 256, 'num_gnn_layers': 4, 'dropout_rate': 0.27746969336800575, 'weight_decay': 0.0009288365358998613, 'optimizer': 'Adam', 'scheduler': 'StepLR', 'huber_delta': 0.1300902824070809, 'spatial_embedding_dim': 128, 'future_embedding_dim': 64, 'lstm_hidden_dim': 128, 'lstm_num_layers': 1}
2025-06-30 01:22:19,478 - trial_39 - ERROR - Trial 39内存不足: CUDA out of memory. Tried to allocate 3.09 GiB. GPU 0 has a total capacity of 23.64 GiB of which 701.56 MiB is free. Including non-PyTorch memory, this process has 22.72 GiB memory in use. Of the allocated memory 19.20 GiB is allocated by PyTorch, and 3.06 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
