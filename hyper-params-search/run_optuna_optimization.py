#!/usr/bin/env python3
"""
一键启动Optuna超参数优化的便捷脚本
包含环境检查、依赖安装、优化执行和结果查看
"""

import os
import sys
import subprocess
import argparse
import json
from datetime import datetime

def check_dependencies():
    """检查必要的依赖是否已安装"""
    required_packages = ['optuna', 'torch', 'torch_geometric']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_optuna_dependencies():
    """安装Optuna相关依赖"""
    print("正在安装Optuna相关依赖...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements_optuna.txt"
        ], check=True)
        print("依赖安装完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"依赖安装失败: {e}")
        return False

def run_optimization(args):
    """运行超参数优化"""
    cmd = [
        sys.executable, "optuna_hyperparameter_tuning.py",
        "--n_trials", str(args.n_trials),
        "--epochs_per_trial", str(args.epochs_per_trial),
        "--timeout", str(args.timeout)
    ]
    
    if args.resume:
        cmd.append("--resume")
    
    if args.prune:
        cmd.append("--prune")
    
    print(f"开始超参数优化...")
    print(f"命令: {' '.join(cmd)}")
    print("="*50)
    
    try:
        subprocess.run(cmd, check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"优化过程出错: {e}")
        return False
    except KeyboardInterrupt:
        print("\n优化被用户中断")
        return False

def show_results():
    """显示优化结果"""
    results_file = "optuna_final_results.json"
    if os.path.exists(results_file):
        with open(results_file, 'r') as f:
            results = json.load(f)
        
        print("\n" + "="*50)
        print("最终优化结果:")
        print("="*50)
        print(f"总试验数: {results['total_trials']}")
        print(f"最佳试验编号: {results['best_trial_number']}")
        print(f"最佳验证损失: {results['best_value']:.6f}")
        print(f"优化耗时: {results['optimization_time_hours']:.2f}小时")
        print(f"时间戳: {results['timestamp']}")
        print("\n最佳超参数:")
        for key, value in results['best_params'].items():
            print(f"  {key}: {value}")
    else:
        print("未找到优化结果文件")

def start_dashboard():
    """启动Web可视化界面"""
    print("\n启动Web可视化界面...")
    try:
        subprocess.run([sys.executable, "optuna_dashboard.py"], check=True)
    except KeyboardInterrupt:
        print("\nWeb界面已关闭")
    except subprocess.CalledProcessError as e:
        print(f"启动Web界面失败: {e}")

def main():
    parser = argparse.ArgumentParser(
        description='一键启动Optuna超参数优化',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python run_optuna_optimization.py                    # 快速开始，使用默认参数
  python run_optuna_optimization.py --quick           # 快速测试模式
  python run_optuna_optimization.py --full            # 完整优化模式
  python run_optuna_optimization.py --dashboard-only  # 仅启动Web界面
        """
    )
    
    # 模式选择
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument('--quick', action='store_true', help='快速测试模式 (10个trials, 10个epochs)')
    mode_group.add_argument('--full', action='store_true', help='完整优化模式 (100个trials, 50个epochs)')
    mode_group.add_argument('--dashboard-only', action='store_true', help='仅启动Web可视化界面')
    
    # 参数设置
    parser.add_argument('--n_trials', type=int, default=50, help='试验次数')
    parser.add_argument('--epochs_per_trial', type=int, default=30, help='每个trial的训练轮数')
    parser.add_argument('--timeout', type=int, default=12*3600, help='超时时间（秒）')
    parser.add_argument('--resume', action='store_true', help='从现有study恢复')
    parser.add_argument('--prune', action='store_true', default=True, help='启用剪枝')
    parser.add_argument('--no-install', action='store_true', help='跳过依赖安装检查')
    parser.add_argument('--no-dashboard', action='store_true', help='优化完成后不启动Web界面')
    
    args = parser.parse_args()
    
    # 设置模式参数
    if args.quick:
        args.n_trials = 10
        args.epochs_per_trial = 10
        args.timeout = 2 * 3600  # 2小时
        print("快速测试模式已启用")
    elif args.full:
        args.n_trials = 100
        args.epochs_per_trial = 50
        args.timeout = 24 * 3600  # 24小时
        print("完整优化模式已启用")
    
    # 仅启动Dashboard
    if args.dashboard_only:
        start_dashboard()
        return
    
    print("Optuna超参数优化启动器")
    print("="*50)
    print(f"试验次数: {args.n_trials}")
    print(f"每试验训练轮数: {args.epochs_per_trial}")
    print(f"超时时间: {args.timeout/3600:.1f}小时")
    print(f"剪枝: {'启用' if args.prune else '禁用'}")
    print(f"恢复模式: {'是' if args.resume else '否'}")
    print("="*50)
    
    # 检查依赖
    if not args.no_install:
        print("检查依赖...")
        missing = check_dependencies()
        if missing:
            print(f"缺少依赖: {missing}")
            install_choice = input("是否自动安装缺少的依赖? (y/n): ")
            if install_choice.lower() in ['y', 'yes']:
                if not install_optuna_dependencies():
                    print("依赖安装失败，请手动安装后重试")
                    return
            else:
                print("请手动安装依赖后重试")
                return
        else:
            print("所有依赖已满足")
    
    # 检查数据文件
    processed_dir = "datasets_processed"
    if not os.path.exists(processed_dir):
        print(f"错误: 未找到预处理数据目录 {processed_dir}")
        print("请先运行数据预处理脚本")
        return
    
    metadata_file = os.path.join(processed_dir, "metadata.pt")
    if not os.path.exists(metadata_file):
        print(f"错误: 未找到元数据文件 {metadata_file}")
        print("请先运行数据预处理脚本")
        return
    
    print("数据文件检查通过")
    
    # 运行优化
    success = run_optimization(args)
    
    if success:
        # 显示结果
        show_results()
        
        # 启动Web界面
        if not args.no_dashboard:
            dashboard_choice = input("\n是否启动Web可视化界面? (y/n): ")
            if dashboard_choice.lower() in ['y', 'yes']:
                start_dashboard()
    else:
        print("优化未成功完成")

if __name__ == "__main__":
    main() 