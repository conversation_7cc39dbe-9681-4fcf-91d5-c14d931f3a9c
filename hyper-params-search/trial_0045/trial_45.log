2025-06-30 01:32:02,056 - trial_45 - INFO - 开始Trial 45
2025-06-30 01:32:02,056 - trial_45 - INFO - 超参数: {'lr': 0.0009280015184273755, 'hidden_dim': 256, 'num_gnn_layers': 4, 'dropout_rate': 0.20988889408435488, 'weight_decay': 0.00010787983549603741, 'optimizer': 'Adam', 'scheduler': 'StepLR', 'huber_delta': 0.1312115426624355, 'spatial_embedding_dim': 128, 'future_embedding_dim': 64, 'lstm_hidden_dim': 128, 'lstm_num_layers': 1}
2025-06-30 01:32:04,712 - trial_45 - ERROR - Trial 45内存不足: CUDA out of memory. Tried to allocate 2.88 GiB. GPU 0 has a total capacity of 23.64 GiB of which 2.30 GiB is free. Including non-PyTorch memory, this process has 21.11 GiB memory in use. Of the allocated memory 17.88 GiB is allocated by PyTorch, and 2.77 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
