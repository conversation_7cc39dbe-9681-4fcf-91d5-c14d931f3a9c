2025-06-29 23:52:34,055 - trial_9 - INFO - 开始Trial 9
2025-06-29 23:52:34,055 - trial_9 - INFO - 超参数: {'lr': 0.00018371989516476596, 'hidden_dim': 256, 'num_gnn_layers': 3, 'dropout_rate': 0.3041970670931533, 'weight_decay': 1.0955097092066528e-05, 'optimizer': 'AdamW', 'scheduler': 'CosineAnnealingLR', 'huber_delta': 0.14054377433622248, 'spatial_embedding_dim': 64, 'future_embedding_dim': 32, 'lstm_hidden_dim': 128, 'lstm_num_layers': 1}
2025-06-29 23:52:38,874 - trial_9 - ERROR - Trial 9内存不足: CUDA out of memory. Tried to allocate 3.19 GiB. GPU 0 has a total capacity of 23.64 GiB of which 3.10 GiB is free. Including non-PyTorch memory, this process has 20.10 GiB memory in use. Of the allocated memory 16.49 GiB is allocated by PyTorch, and 3.16 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
