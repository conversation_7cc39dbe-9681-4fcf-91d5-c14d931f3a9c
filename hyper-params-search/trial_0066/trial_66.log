2025-06-30 02:34:20,852 - trial_66 - INFO - 开始Trial 66
2025-06-30 02:34:20,852 - trial_66 - INFO - 超参数: {'lr': 0.006274667618294163, 'hidden_dim': 256, 'num_gnn_layers': 4, 'dropout_rate': 0.35171110722759746, 'weight_decay': 0.0002207329912164689, 'optimizer': 'AdamW', 'scheduler': 'StepLR', 'huber_delta': 0.198142638335207, 'spatial_embedding_dim': 128, 'future_embedding_dim': 64, 'lstm_hidden_dim': 128, 'lstm_num_layers': 2}
2025-06-30 02:34:21,132 - trial_66 - ERROR - Trial 66内存不足: CUDA out of memory. Tried to allocate 3.19 GiB. GPU 0 has a total capacity of 23.64 GiB of which 119.56 MiB is free. Including non-PyTorch memory, this process has 23.29 GiB memory in use. Of the allocated memory 19.84 GiB is allocated by PyTorch, and 2.99 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
