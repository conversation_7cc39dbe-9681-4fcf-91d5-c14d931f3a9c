2025-06-30 01:27:05,571 - trial_42 - INFO - 开始Trial 42
2025-06-30 01:27:05,571 - trial_42 - INFO - 超参数: {'lr': 0.0020886042906741414, 'hidden_dim': 256, 'num_gnn_layers': 4, 'dropout_rate': 0.23776171782113475, 'weight_decay': 8.481230454280699e-05, 'optimizer': 'AdamW', 'scheduler': 'CosineAnnealingLR', 'huber_delta': 0.14414868022859129, 'spatial_embedding_dim': 64, 'future_embedding_dim': 32, 'lstm_hidden_dim': 64, 'lstm_num_layers': 1}
2025-06-30 01:27:06,454 - trial_42 - ERROR - Trial 42内存不足: CUDA out of memory. Tried to allocate 2.88 GiB. GPU 0 has a total capacity of 23.64 GiB of which 2.24 GiB is free. Including non-PyTorch memory, this process has 21.16 GiB memory in use. Of the allocated memory 17.88 GiB is allocated by PyTorch, and 2.83 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
