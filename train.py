# train.py - 正确的优化版本：保持原始网络架构，只优化数据预处理
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import json
import os
import random
import logging
import argparse
import time
from tqdm import tqdm
from collections import defaultdict
import itertools

from clos_topo import Clos
# 导入原始模型架构
from model import (
    STGNNModel, FUTURE_WINDOW, TIME_SLICES, SPATIAL_EMBEDDING_DIM, 
    FUTURE_EMBEDDING_DIM, HIDDEN_DIM, NUM_GNN_LAYERS, DEVICE
)

# ==================== 全局配置与超参数 ====================
# 训练相关参数
FILE_BATCH_SIZE = 1
DROPOUT_RATE = 0.2

# 设置随机种子
RANDOM_SEED = 42
torch.manual_seed(RANDOM_SEED)
np.random.seed(RANDOM_SEED)
random.seed(RANDOM_SEED)
if torch.cuda.is_available():
    torch.cuda.manual_seed_all(RANDOM_SEED)

print(f"使用设备: {DEVICE}")

# ==================== 链路图构建器 ====================
class LinkGraphBuilder:
    def __init__(self, clos_topology: Clos):
        self.clos_topology = clos_topology
        self.link_to_idx = {}
        self.idx_to_link = {}
        self.edge_index = None
        self.num_links = 0
        self._build_link_mapping()
        self._build_link_graph()

    def _build_link_mapping(self):
        # 确保边的顺序一致，以便重现性
        sorted_edges = sorted(self.clos_topology.G.edges())
        self.link_to_idx = {(src, dst): i for i, (src, dst) in enumerate(sorted_edges)}
        self.idx_to_link = {i: link for link, i in self.link_to_idx.items()}
        self.num_links = len(self.link_to_idx)

    def _build_link_graph(self):
        edge_list = []
        switch_to_links = defaultdict(list)
        for link_idx, (src, dst) in self.idx_to_link.items():
            if src.startswith(('S', 'P')): switch_to_links[src].append(link_idx)
            if dst.startswith(('S', 'P')): switch_to_links[dst].append(link_idx)
        for _, connected_link_indices in switch_to_links.items():
            if len(connected_link_indices) > 1:
                for u, v in itertools.permutations(connected_link_indices, 2):
                    edge_list.append([u, v])
        if edge_list:
            unique_edges = list(set(map(tuple, edge_list)))
            self.edge_index = torch.tensor(unique_edges, dtype=torch.long).t().contiguous()
        else:
            self.edge_index = torch.empty((2, 0), dtype=torch.long)

    def get_path_link_indices(self, path: list) -> list:
        """将节点路径转换为链路索引列表，如果路径无效返回None"""
        if len(path) < 2: 
            return None
        
        indices = []
        for i in range(len(path) - 1):
            link_idx = self.link_to_idx.get((path[i], path[i+1]))
            if link_idx is None:
                return None  # 如果任何链路无效，返回None
            indices.append(link_idx)
        
        return indices

    def get_total_links(self) -> int:
        return self.num_links

# ==================== 数据集类 ====================
class SimulationDataset(Dataset):
    def __init__(self, data_dir):
        self.data_dir = data_dir
        self.file_list = sorted([os.path.join(data_dir, f) for f in os.listdir(data_dir) if f.endswith('.pt')])

    def __len__(self):
        return len(self.file_list)

    def __getitem__(self, idx):
        return torch.load(self.file_list[idx], weights_only=True)

# ==================== 数据适配器 ====================  
class PreprocessedDataAdapter:
    """将预处理的tensor数据转换为原始STGNNModel期望的batch_data格式"""
    def __init__(self, link_builder: LinkGraphBuilder, global_mean: float, global_std: float):
        self.link_builder = link_builder
        self.global_mean = global_mean
        self.global_std = global_std
        
    def convert_tensor_to_batch_data(self, file_tensor: torch.Tensor):
        """
        将预处理的10元素tensor转换为原始STGNNModel期望的batch_data格式
        
        Args:
            file_tensor: [N, 10] tensor，包含 [start, end, norm_size, p1, p2, p3, p4, delay, sync_count, sync_volume]
            
        Returns:
            List of batch_data dictionaries for each sample
        """
        batch_data_list = []
        file_tensor_cpu = file_tensor.cpu()
        
        # 为每个时刻的流创建batch
        event_times = torch.unique(file_tensor_cpu[:, 0])
        
        for current_time in event_times:
            # 获取当前时刻开始的流
            event_flows_mask = file_tensor_cpu[:, 0] == current_time
            event_flows_indices = torch.where(event_flows_mask)[0]
            
            if len(event_flows_indices) == 0:
                continue
            
            # 构造当前时刻的all_flows和flows
            all_flows = []
            flows = []
            
            for i in range(file_tensor_cpu.shape[0]):
                flow_info = file_tensor_cpu[i]
                start_time = flow_info[0].item()
                end_time = flow_info[1].item()
                norm_size = flow_info[2].item()
                path_links = flow_info[3:7].long().tolist()
                time_delay = flow_info[7].item()
                
                # 反向构造路径（从链路索引到节点路径）
                path = self._reconstruct_path_from_links(path_links)
                if path is None:
                    continue
                
                # 反向标准化流大小
                flow_size = norm_size * self.global_std + self.global_mean
                
                flow_dict = {
                    'inputs': {
                        'flow_id': f"flow_{i}",
                        'start_time': start_time,
                        'path': path,
                        'flow_features': [flow_size]
                    },
                    'time_delay': time_delay
                }
                
                all_flows.append(flow_dict)
                
                # 如果是当前时刻开始的流，加入flows列表
                if abs(start_time - current_time.item()) < 1e-6:
                    flows.append(flow_dict)
            
            if flows:
                batch_data_list.append({
                    'flows': flows,
                    'all_flows': all_flows
                })
        
        return batch_data_list
    
    def _reconstruct_path_from_links(self, link_indices):
        """从链路索引重构节点路径"""
        if not link_indices or len(link_indices) != 4:
            return None
            
        path = []
        for i, link_idx in enumerate(link_indices):
            if link_idx not in self.link_builder.idx_to_link:
                return None
                
            src, dst = self.link_builder.idx_to_link[link_idx]
            if i == 0:
                path.extend([src, dst])
            else:
                # 检查连续性
                if path[-1] != src:
                    return None
                path.append(dst)
        
        return path

# ==================== 指标计算函数 ====================
def compute_metrics(predictions, targets):
    """计算MAE、RMSE、MAPE三个指标"""
    predictions = np.array(predictions)
    targets = np.array(targets)
    
    mae = np.mean(np.abs(predictions - targets))
    rmse = np.sqrt(np.mean((predictions - targets) ** 2))
    
    non_zero_mask = targets != 0
    if np.sum(non_zero_mask) > 0:
        mape = np.mean(np.abs((targets[non_zero_mask] - predictions[non_zero_mask]) / targets[non_zero_mask])) * 100
    else:
        mape = float('inf')
    
    return {
        'mae': mae,
        'rmse': rmse,
        'mape': mape
    }

# ==================== 训练与评估 ====================
def run_one_epoch(model, data_loader, optimizer, loss_fn, data_adapter):
    model.train()
    total_loss = 0
    num_samples = 0
    
    for file_tensor_batch in tqdm(data_loader, desc="Training"):
        file_tensor = file_tensor_batch[0]
        
        # 使用数据适配器转换为原始STGNNModel期望的格式
        batch_data_list = data_adapter.convert_tensor_to_batch_data(file_tensor)
        
        for batch_data in batch_data_list:
            if not batch_data['flows']:
                continue
                
            optimizer.zero_grad()
            
            # 使用原始STGNNModel进行预测
            predictions = model(batch_data)
            
            # 构建目标张量
            targets = torch.tensor([flow['time_delay'] for flow in batch_data['flows']], 
                                 dtype=torch.float32, device=DEVICE)
            targets = torch.log1p(targets)  # 对数变换保持一致
            
            loss = loss_fn(predictions.squeeze(), targets.squeeze())
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            num_samples += len(batch_data['flows'])

    return total_loss / num_samples if num_samples > 0 else 0.0

def evaluate(model, data_loader, loss_fn, data_adapter, return_preds=False, compute_detailed_metrics=False):
    model.eval()
    total_loss = 0
    num_samples = 0
    all_predictions, all_targets = [], []
    
    with torch.no_grad():
        for file_tensor_batch in tqdm(data_loader, desc="Evaluating"):
            file_tensor = file_tensor_batch[0]
            
            batch_data_list = data_adapter.convert_tensor_to_batch_data(file_tensor)
            
            for batch_data in batch_data_list:
                if not batch_data['flows']:
                    continue
                
                predictions = model(batch_data)
                
                targets = torch.tensor([flow['time_delay'] for flow in batch_data['flows']], 
                                     dtype=torch.float32, device=DEVICE)
                targets = torch.log1p(targets)
                
                loss = loss_fn(predictions.squeeze(), targets.squeeze())
                total_loss += loss.item()
                num_samples += len(batch_data['flows'])
                
                # 收集预测和目标用于指标计算
                pred_values = torch.expm1(predictions.squeeze()).cpu().numpy()
                target_values = torch.expm1(targets.squeeze()).cpu().numpy()
                
                if pred_values.ndim == 0:
                    all_predictions.append(float(pred_values))
                    all_targets.append(float(target_values))
                else:
                    all_predictions.extend(pred_values.tolist())
                    all_targets.extend(target_values.tolist())

    avg_loss = total_loss / num_samples if num_samples > 0 else 0.0
    
    if len(all_predictions) > 0 and len(all_targets) > 0:
        metrics = compute_metrics(all_predictions, all_targets)
    else:
        metrics = {'mae': 0.0, 'rmse': 0.0, 'mape': 0.0}
    
    if return_preds:
        return avg_loss, all_predictions, all_targets, metrics
    elif compute_detailed_metrics:
        return avg_loss, metrics
    else:
        return avg_loss

# ==================== 主函数 ====================
def setup_logging():
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s',
                        handlers=[logging.FileHandler('training_efficient.log', mode='w'), logging.StreamHandler()])

def main():
    setup_logging()
    parser = argparse.ArgumentParser(description='正确优化版：保持原始网络架构，只优化数据预处理')
    parser.add_argument('--processed_dir', type=str, default='datasets_processed', help='预处理后数据集的根目录')
    parser.add_argument('--epochs', type=int, default=200, help='训练轮次')
    parser.add_argument('--lr', type=float, default=1e-3, help='学习率')
    parser.add_argument('--model_save_path', type=str, default='best_model.pth', help='模型保存路径')
    args = parser.parse_args()
    logging.info(f"参数: {args}")
    
    # 构建Clos拓扑
    clos = Clos(4, 4, 4)
    clos.build()
    link_builder = LinkGraphBuilder(clos)
    
    # 加载元数据
    metadata = torch.load(os.path.join(args.processed_dir, 'metadata.pt'))
    global_mean = metadata.get('global_mean', 0.0)
    global_std = metadata.get('global_std', 1.0)
    
    logging.info(f"加载的全局统计信息: mean={global_mean:.4f}, std={global_std:.4f}")
    
    # 模型超参数
    hyperparams = {
        'spatial_embedding_dim': SPATIAL_EMBEDDING_DIM,
        'future_embedding_dim': FUTURE_EMBEDDING_DIM,
        'hidden_dim': HIDDEN_DIM,
        'num_gnn_layers': NUM_GNN_LAYERS
    }
    
    # 创建原始STGNNModel - 保持网络架构不变！
    model = STGNNModel(clos, **hyperparams).to(DEVICE)
    logging.info(f"原始STGNNModel参数总数: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
    # 更新模型的全局统计信息
    model.feature_engineer.global_mean = global_mean
    model.feature_engineer.global_std = global_std
    
    # 优化器和调度器
    optimizer = optim.AdamW(model.parameters(), lr=args.lr, weight_decay=1e-4)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.1, patience=5,    
                                    verbose=False, threshold=0.001, threshold_mode='rel', cooldown=0, min_lr=2e-6, eps=1e-08)
    
    # 损失函数
    loss_fn = nn.L1Loss()  # 使用MAE作为损失函数
    
    # 数据适配器 - 将预处理tensor转换为原始模型期望的格式
    data_adapter = PreprocessedDataAdapter(link_builder, global_mean, global_std)
    
    # 数据加载器
    train_dataset = SimulationDataset(data_dir=os.path.join(args.processed_dir, 'train'))
    train_loader = DataLoader(train_dataset, batch_size=FILE_BATCH_SIZE, shuffle=True, num_workers=4, pin_memory=True)
    
    val_dataset = SimulationDataset(data_dir=os.path.join(args.processed_dir, 'validation'))
    val_loader = DataLoader(val_dataset, batch_size=FILE_BATCH_SIZE, shuffle=False, num_workers=4)
    
    # 训练循环
    best_val_loss = float('inf')
    history = {'train_loss': [], 'val_loss': []}

    for epoch in range(args.epochs):
        logging.info(f"\n--- Epoch {epoch+1}/{args.epochs} ---")
        
        # 训练
        avg_train_loss = run_one_epoch(model, train_loader, optimizer, loss_fn, data_adapter)
        history['train_loss'].append(avg_train_loss)

        # 验证
        avg_val_loss, val_metrics = evaluate(model, val_loader, loss_fn, data_adapter, compute_detailed_metrics=True)
        history['val_loss'].append(avg_val_loss)
        
        # 输出训练信息
        logging.info(f"Epoch {epoch+1} | 训练损失: {avg_train_loss:.6f} | 验证损失: {avg_val_loss:.6f} | 学习率: {optimizer.param_groups[0]['lr']:.6f}")
        logging.info(f"验证指标 - MAE: {val_metrics['mae']:.6f} | RMSE: {val_metrics['rmse']:.6f} | MAPE: {val_metrics['mape']:.2f}%")
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        # 保存最优模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            logging.info(f"发现更优模型，验证损失降至 {best_val_loss:.6f}。模型已保存。")
            checkpoint = {
                'model_state_dict': model.state_dict(),
                'hyperparams': hyperparams,
                'metadata': metadata,
                'model_type': 'original'  # 标记为原始模型（使用预处理数据训练）
            }
            torch.save(checkpoint, args.model_save_path)

    logging.info("\n🎉 正确优化训练完成！")
    logging.info(f"✅ 保持了原始STGNNModel架构")
    logging.info(f"✅ 只优化了数据预处理流程")
    logging.info(f"最佳验证损失: {best_val_loss:.6f}")

    # 保存训练历史
    with open('training_history.json', 'w') as f:
        json.dump(history, f, indent=4)
    logging.info("训练历史已保存至 training_history.json")

if __name__ == "__main__":
    main()
