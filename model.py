#!/usr/bin/env python3
#model.py
"""
时空图注意力网络模型 (A Future-Aware Spatio-Temporal Graph Attention Network)
用于预测单个网络流的端到端流完成时延 (FCT)

核心改进：使用GAT (Graph Attention Network) 替代GCN，
能够学习不同链路间的重要性权重，更适合网络流量预测场景。

Author: 基于RIPER-6模式协议实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, BatchNorm

import numpy as np
import json
import os
import random
from collections import defaultdict
from typing import List, Dict, Tuple, Optional, Union
import itertools # 导入itertools用于生成组合

# 导入项目模块
from clos_topo import Clos

# ==================== 全局超参数常量 ====================
FUTURE_WINDOW = 2.0          # 未来预测时间窗口(秒)
TIME_SLICES = 5            # 时间切片数量
SPATIAL_EMBEDDING_DIM = 128  # 空间嵌入维度  
FUTURE_EMBEDDING_DIM = 64    # 未来嵌入维度
HIDDEN_DIM = 256             # 隐藏层维度
NUM_GNN_LAYERS = 3           # 图神经网络层数

# 模型相关参数
BATCH_SIZE = 16
DROPOUT_RATE = 0.1

# LSTM聚合层参数
LSTM_HIDDEN_DIM = 128
LSTM_NUM_LAYERS = 2

# 设备配置
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# 设置随机种子
RANDOM_SEED = 42
torch.manual_seed(RANDOM_SEED)
np.random.seed(RANDOM_SEED)
random.seed(RANDOM_SEED)

# ==================== 链路图构建器 ====================
class LinkGraphBuilder:
    """将物理拓扑转换为以单向链路为节点的图结构"""
    
    def __init__(self, clos_topology: Clos):
        """
        初始化并构建链路图
        
        Args:
            clos_topology: Clos网络拓扑对象
        """
        self.clos_topology = clos_topology
        self.link_to_idx = {}      # 链路到索引的映射 {(src, dst): idx}
        self.idx_to_link = {}      # 索引到链路的映射 {idx: (src, dst)}
        self.edge_index = None     # 链路图的边索引张量 [2, num_edges]
        self.num_links = 0         # 总链路数量
        
        # 构建链路图
        self._build_link_mapping()
        self._build_link_graph()
        
        print(f"链路图构建完成: 总共 {self.num_links} 条链路")

    def _build_link_mapping(self):
        """创建链路到索引的映射字典"""
        link_idx = 0
        
        # 遍历物理拓扑的所有边，创建单向链路映射
        for edge in self.clos_topology.G.edges():
            src, dst = edge
            # 每条物理边对应一条单向链路
            self.link_to_idx[(src, dst)] = link_idx
            self.idx_to_link[link_idx] = (src, dst)
            link_idx += 1
            
        self.num_links = link_idx
        print(f"创建链路映射: {self.num_links} 条单向链路")

    def _build_link_graph(self):
        """
        【已修改】构建链路间的连接关系。
        新逻辑：如果两条链路连接到同一个交换机，则它们之间有边。
        """
        edge_list = []
        # 创建一个字典，用于存储每个交换机连接了哪些链路
        switch_to_links = defaultdict(list)

        # 遍历所有链路，填充上述字典
        for link_idx, (src, dst) in self.idx_to_link.items():
            # 假设交换机节点以 'S' 或 'P' 开头
            if src.startswith(('S', 'P')):
                switch_to_links[src].append(link_idx)
            if dst.startswith(('S', 'P')):
                switch_to_links[dst].append(link_idx)

        # 遍历每个交换机连接的链路列表
        for switch_node, connected_link_indices in switch_to_links.items():
            # 如果一个交换机只连接了一条链路，则无法形成边
            if len(connected_link_indices) > 1:
                # 在连接到同一个交换机的所有链路之间创建全连接边
                # itertools.permutations会生成所有有序对，正好是双向边
                for u, v in itertools.permutations(connected_link_indices, 2):
                    edge_list.append([u, v])

        if edge_list:
            # 去除重复的边并创建张量
            self.edge_index = torch.tensor(list(set(tuple(i) for i in edge_list)), dtype=torch.long).t().contiguous()
        else:
            # 如果没有边，创建空的边索引
            self.edge_index = torch.empty((2, 0), dtype=torch.long)

        print(f"【新逻辑】链路图边数: {self.edge_index.size(1)}")

    def get_path_link_indices(self, path: List[str]) -> List[int]:
        """
        将路径节点列表转换为链路索引列表
        
        Args:
            path: 节点路径列表，如 ["H2", "S1", "P5", "S13", "H52"]
            
        Returns:
            链路索引列表，如 [15, 28, 105, 142]
        """
        if len(path) < 2:
            return []
            
        link_indices = []
        for i in range(len(path) - 1):
            src, dst = path[i], path[i + 1]
            link = (src, dst)
            
            if link in self.link_to_idx:
                link_indices.append(self.link_to_idx[link])
            else:
                raise ValueError(f"链路 {link} 在拓扑中不存在")
                
        return link_indices

    def get_total_links(self) -> int:
        """返回总链路数量"""
        return self.num_links


# ==================== 特征工程器 ====================
class FeatureEngineer:
    """负责计算当前空间拥塞特征和未来时间竞争特征"""
    
    def __init__(self, link_graph_builder: LinkGraphBuilder, 
                 future_window: float = FUTURE_WINDOW, 
                 time_slices: int = TIME_SLICES,
                 global_mean: float = 0.0,
                 global_std: float = 1.0):
        """
        初始化特征工程器
        
        Args:
            link_graph_builder: 链路图构建器
            future_window: 未来预测时间窗口
            time_slices: 时间切片数量
            global_mean: 全局流大小均值
            global_std: 全局流大小标准差
        """
        self.link_graph_builder = link_graph_builder
        self.future_window = future_window
        self.time_slices = time_slices
        self.slice_duration = future_window / time_slices
        self.global_mean = global_mean
        self.global_std = global_std
        
        # 定义链路带宽映射表 (Gbps为单位) - 所有链路统一为400Gbps
        self.link_bandwidth_mapping = {
            'core': 400.0,      # 核心层链路: 400Gbps
            'aggregation': 400.0, # 汇聚层链路: 400Gbps
            'access': 400.0      # 接入层链路: 400Gbps
        }
        
        # 物理链路带宽常量
        self.PHYSICAL_BANDWIDTH = 400.0
        
    def _get_dynamic_link_bandwidth(self, link_idx: int, current_time: float, target_flow: Dict, all_flows: List[Dict]) -> float:
        """
        根据链路索引和当前时刻活跃流情况计算动态可用带宽
        
        Args:
            link_idx: 链路索引
            current_time: 当前时间
            target_flow: 目标流数据
            all_flows: 所有流数据列表
            
        Returns:
            动态可用带宽 (归一化后)
        """
        # 获取该链路上的活跃流，排除目标流本身
        active_flows_on_link = self._get_active_flows_on_link(link_idx, current_time, all_flows, exclude_flow=target_flow)
        
        # 计算活跃流总数N（不包括目标流）
        N = len(active_flows_on_link)
        
        # 计算动态可用带宽: 物理带宽 / (N + 1)
        dynamic_bandwidth = self.PHYSICAL_BANDWIDTH / (N + 1)
        
        # 归一化到[0,1]区间
        return dynamic_bandwidth / self.PHYSICAL_BANDWIDTH
        
    def compute_current_spatial_features(self, current_time: float,
                                       target_flow: Dict, all_flows: List[Dict]) -> torch.Tensor:
        """
        计算瓶颈感知的空间特征

        Args:
            current_time: 当前时间
            target_flow: 目标流数据
            all_flows: 所有流的数据列表

        Returns:
            形状为 [num_links, 4] 的张量，表示每条链路的瓶颈感知特征
            特征维度：[可用带宽, 竞争数量, 总负载, 瓶颈标识]
        """
        num_links = self.link_graph_builder.get_total_links()
        # 扩展特征维度：[可用带宽, 竞争数量, 总负载, 瓶颈标识]
        spatial_features = torch.zeros((num_links, 4))

        # 计算每条链路的竞争情况和可用带宽
        link_contentions = {}
        for link_idx in range(num_links):
            active_flows_on_link = self._get_active_flows_on_link(link_idx, current_time, all_flows, exclude_flow=target_flow)
            contention_count = len(active_flows_on_link)

            # 使用仿真中的带宽分配逻辑：capacity / (contention + 1)
            available_bandwidth = self.PHYSICAL_BANDWIDTH / (contention_count + 1)

            # 计算总负载
            total_load = 0.0
            for flow in active_flows_on_link:
                flow_size = flow['inputs']['flow_features'][0]
                normalized_size = (flow_size - self.global_mean) / self.global_std if self.global_std > 0 else 0
                total_load += normalized_size

            link_contentions[link_idx] = {
                'available_bw': available_bandwidth,
                'contention_count': contention_count,
                'total_load': total_load
            }

            # 填充前3维特征
            spatial_features[link_idx, 0] = available_bandwidth / self.PHYSICAL_BANDWIDTH  # 归一化可用带宽
            spatial_features[link_idx, 1] = contention_count / 10.0  # 归一化竞争数量
            spatial_features[link_idx, 2] = total_load  # 归一化总负载

        # 识别瓶颈链路（可用带宽最小的链路）
        if link_contentions:
            min_available_bw = min(lc['available_bw'] for lc in link_contentions.values())
            for link_idx, lc in link_contentions.items():
                if lc['available_bw'] == min_available_bw:
                    spatial_features[link_idx, 3] = 1.0  # 瓶颈标识

        return spatial_features
    
    def compute_future_temporal_features(self, flow_path_links: List[int], 
                                       start_time: float, 
                                       all_flows: List[Dict]) -> torch.Tensor:
        """
        计算未来时间竞争特征
        
        Args:
            flow_path_links: 流路径上的链路索引列表
            start_time: 流开始时间
            all_flows: 所有流的数据列表
            
        Returns:
            形状为 [path_length, time_slices] 的张量，表示路径上每条链路的未来竞争向量
        """
        path_length = len(flow_path_links)
        future_features = torch.zeros((path_length, self.time_slices))
        
        # 对于路径上的每条链路，收集未来竞争事件
        for path_idx, link_idx in enumerate(flow_path_links):
            future_events = []
            
            # 遍历所有流，找到在未来时间窗口内与当前链路竞争的流
            for flow in all_flows:
                flow_start_time = flow['inputs']['start_time']
                
                # 只考虑在未来时间窗口内开始的流（排除同步流）
                if start_time < flow_start_time <= start_time + self.future_window:
                    flow_path = flow['inputs']['path']
                    
                    try:
                        # 检查这个流是否经过当前链路
                        # 这段代码的目的是检测未来流量是否会经过当前链路，并记录相关信息
                        # 1. 首先获取未来流经过的所有链路索引
                        flow_link_indices = self.link_graph_builder.get_path_link_indices(flow_path)
                        
                        # 2. 检查当前链路是否在未来流的路径上
                        if link_idx in flow_link_indices:
                            # 3. 计算未来流相对于当前流的开始时间差
                            relative_time = flow_start_time - start_time
                            
                            # 4. 获取未来流的大小
                            flow_size = flow['inputs']['flow_features'][0]
                            
                            # 5. 对流量大小进行标准化处理，避免不同规模的流量对模型造成不平衡影响
                            normalized_flow_size = (flow_size - self.global_mean) / self.global_std if self.global_std > 0 else 0
                            
                            # 6. 将这个未来竞争事件（时间差和标准化流量大小）添加到事件列表中
                            future_events.append((relative_time, normalized_flow_size))
                    except ValueError:
                        continue
            
            # 将未来事件切片为固定长度向量
            future_vector = self._slice_future_events(future_events)
            future_features[path_idx] = future_vector
            
        return future_features
    
    def compute_proactive_contention_features(self, target_flow: Dict, all_flows: List[Dict]) -> torch.Tensor:
        """
        计算增强的冲击特征，包含近似同步和CCG组协调

        Args:
            target_flow: 目标流数据
            all_flows: 所有流的数据列表

        Returns:
            形状为 [6] 的张量，包含增强的冲击特征
            [瓶颈同步数, 瓶颈同步量, 瓶颈CCG数, 瓶颈CCG量, 路径平均CCG竞争, CCG组大小]
        """
        target_start_time = target_flow['inputs']['start_time']
        # ✅ 处理训练数据中可能缺失的ccg_id
        target_ccg = target_flow['inputs'].get('ccg_id', f"default_ccg_{hash(str(target_flow['inputs']['flow_id'])) % 100}")
        target_path_links = self.link_graph_builder.get_path_link_indices(target_flow['inputs']['path'])

        # 1. 近似同步流检测（时间窗口内的流）
        sync_time_window = 0.5  # 0.5秒内的流视为近似同步
        near_sync_flows = []
        same_ccg_flows = []

        for flow in all_flows:
            if flow is target_flow:
                continue

            flow_start_time = flow['inputs']['start_time']
            time_diff = abs(flow_start_time - target_start_time)

            # 近似同步流
            if time_diff <= sync_time_window:
                near_sync_flows.append(flow)

            # 同CCG组流（更重要的协调关系）
            flow_ccg = flow['inputs'].get('ccg_id', f"default_ccg_{hash(str(flow['inputs']['flow_id'])) % 100}")
            if flow_ccg == target_ccg:
                same_ccg_flows.append(flow)

        # 2. 计算路径上每条链路的竞争特征
        link_features = []
        for link_idx in target_path_links:
            # 近似同步竞争
            sync_count = 0
            sync_volume = 0.0

            # CCG组内竞争（更重要）
            ccg_count = 0
            ccg_volume = 0.0

            # 检查近似同步流
            for flow in near_sync_flows:
                try:
                    flow_path_links = self.link_graph_builder.get_path_link_indices(flow['inputs']['path'])
                    if link_idx in flow_path_links:
                        sync_count += 1
                        flow_size = flow['inputs']['flow_features'][0]
                        normalized_size = (flow_size - self.global_mean) / self.global_std if self.global_std > 0 else 0
                        sync_volume += normalized_size
                except ValueError:
                    continue

            # 检查同CCG组流
            for flow in same_ccg_flows:
                try:
                    flow_path_links = self.link_graph_builder.get_path_link_indices(flow['inputs']['path'])
                    if link_idx in flow_path_links:
                        ccg_count += 1
                        flow_size = flow['inputs']['flow_features'][0]
                        normalized_size = (flow_size - self.global_mean) / self.global_std if self.global_std > 0 else 0
                        ccg_volume += normalized_size
                except ValueError:
                    continue

            link_features.append([sync_count, sync_volume, ccg_count, ccg_volume])

        # 3. 瓶颈感知聚合：找到真正的瓶颈链路
        if not link_features:
            return torch.zeros(6, dtype=torch.float32)

        import numpy as np
        link_features = np.array(link_features)

        # 计算每条链路的总竞争强度（同步 + CCG组，CCG组权重更高）
        total_competition = link_features[:, 0] + link_features[:, 2] * 2.0  # CCG组竞争权重翻倍
        bottleneck_idx = np.argmax(total_competition)

        # 提取瓶颈链路和全路径的特征
        bottleneck_features = link_features[bottleneck_idx]
        path_avg_features = np.mean(link_features, axis=0)

        return torch.tensor([
            bottleneck_features[0],  # 瓶颈链路近似同步数量
            bottleneck_features[1],  # 瓶颈链路近似同步流量
            bottleneck_features[2],  # 瓶颈链路CCG组数量
            bottleneck_features[3],  # 瓶颈链路CCG组流量
            path_avg_features[2],    # 路径平均CCG组竞争
            len(same_ccg_flows) / 10.0  # 总CCG组大小（归一化）
        ], dtype=torch.float32)
    
    def _get_active_flows_on_link(self, link_idx: int, current_time: float, all_flows: List[Dict], exclude_flow: Dict = None) -> List[Dict]:
        """
        获取指定链路在指定时间的活跃流列表

        Args:
            link_idx: 链路索引
            current_time: 当前时间
            all_flows: 所有流数据列表
            exclude_flow: 需要排除的流（通常是目标流本身）

        Returns:
            活跃流列表
        """
        active_flows_on_link = []

        for flow in all_flows:
            # 如果是需要排除的流，则跳过
            if exclude_flow is not None and flow is exclude_flow:
                continue

            start_time = flow['inputs']['start_time']

            # ✅ 修复数据泄露：使用预测的完成时间而不是真实时间
            if 'predicted_end_time' in flow and flow['predicted_end_time'] is not None:
                # 使用预测的结束时间
                end_time = flow['predicted_end_time']
            elif 'predicted_time_delay' in flow and flow['predicted_time_delay'] is not None:
                # 使用预测的时延
                end_time = start_time + flow['predicted_time_delay']
            else:
                # 未预测的流：如果还没开始，跳过；如果已开始但未预测，视为仍在传输
                if start_time > current_time:
                    continue  # 还未开始的流
                else:
                    # 已开始但未预测完成时间的流，暂时视为活跃（保守估计）
                    end_time = float('inf')

            # 判断流是否在当前时间活跃
            if start_time <= current_time < end_time:
                # 检查该流是否经过当前链路
                flow_path = flow['inputs']['path']
                try:
                    flow_link_indices = self.link_graph_builder.get_path_link_indices(flow_path)
                    if link_idx in flow_link_indices:
                        active_flows_on_link.append(flow)
                except ValueError:
                    # 忽略无效路径
                    continue

        return active_flows_on_link
    
    def _get_active_flows_at_time(self, target_time: float,
                                all_flows: List[Dict]) -> List[Dict]:
        """
        获取指定时间的活跃流

        Args:
            target_time: 目标时间
            all_flows: 所有流的数据列表

        Returns:
            活跃流列表
        """
        active_flows = []

        for flow in all_flows:
            start_time = flow['inputs']['start_time']

            # ✅ 修复数据泄露：使用预测的完成时间
            if 'predicted_end_time' in flow and flow['predicted_end_time'] is not None:
                end_time = flow['predicted_end_time']
            elif 'predicted_time_delay' in flow and flow['predicted_time_delay'] is not None:
                end_time = start_time + flow['predicted_time_delay']
            else:
                # 未预测的流处理
                if start_time > target_time:
                    continue  # 还未开始
                else:
                    end_time = float('inf')  # 已开始但未预测，保守估计为活跃

            # 判断流是否在目标时间活跃
            if start_time <= target_time < end_time:
                active_flows.append(flow)

        return active_flows
    
    def _slice_future_events(self, future_events: List[Tuple[float, float]]) -> torch.Tensor:
        """
        将未来事件切片为固定长度向量
        
        Args:
            future_events: 未来事件列表 [(时间, 流大小), ...]
            
        Returns:
            长度为 time_slices 的向量
        """
        vector = torch.zeros(self.time_slices)
        
        for event_time, flow_size in future_events:
            # 计算事件属于哪个时间片
            slice_idx = int(event_time / self.slice_duration)
            # 确保索引在有效范围内
            if 0 <= slice_idx < self.time_slices:
                vector[slice_idx] += flow_size
                
        return vector
    
    def extract_flow_features(self, flow_data: Dict, all_flows: List[Dict] = None) -> torch.Tensor:
        """
        提取单个流的自身特征 - 2维特征

        Args:
            flow_data: 流数据字典
            all_flows: 所有流数据列表（未使用，保留接口兼容性）

        Returns:
            2维流特征向量
        """
        # === 基础特征 ===
        flow_size = flow_data['inputs']['flow_features'][0]
        normalized_flow_size = (flow_size - self.global_mean) / self.global_std if self.global_std > 0 else 0
        
        # === 时间上下文特征 ===
        start_time = flow_data['inputs']['start_time']
        normalized_start_time = start_time / 100.0  # 假设时间范围0-100
        
        # 组合2维特征
        features = torch.tensor([
            normalized_flow_size,           # 0: 归一化流大小
            normalized_start_time,          # 1: 归一化开始时间
        ], dtype=torch.float32)
        
        return features


# ==================== 空间图注意力网络模块 ====================
class SGNNModule(nn.Module):
    """空间图注意力网络模块，使用GAT编码整个网络的空间拥塞关系"""
    
    def __init__(self, input_dim: int, hidden_dim: int = HIDDEN_DIM, 
                 output_dim: int = SPATIAL_EMBEDDING_DIM, 
                 num_layers: int = NUM_GNN_LAYERS,
                 heads: int = 2, return_attention: bool = False):
        """
        初始化GAT层
        
        Args:
            input_dim: 输入特征维度
            hidden_dim: 隐藏层维度
            output_dim: 输出嵌入维度
            num_layers: GAT层数
            heads: 注意力头数
        """
        super(SGNNModule, self).__init__()
        
        self.num_layers = num_layers
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.heads = heads
        self.return_attention = return_attention
        
        # 构建GAT层
        self.conv_layers = nn.ModuleList()
        self.batch_norms = nn.ModuleList()
        
        # 第一层 - 多头注意力，拼接输出
        self.conv_layers.append(GATConv(input_dim, hidden_dim // heads, heads=heads, concat=True, dropout=DROPOUT_RATE))
        self.batch_norms.append(BatchNorm(hidden_dim))
        
        # 中间层 - 多头注意力，拼接输出
        for _ in range(num_layers - 2):
            self.conv_layers.append(GATConv(hidden_dim, hidden_dim // heads, heads=heads, concat=True, dropout=DROPOUT_RATE))
            self.batch_norms.append(BatchNorm(hidden_dim))
        
        # 最后一层 - 多头注意力，平均输出
        if num_layers > 1:
            self.conv_layers.append(GATConv(hidden_dim, output_dim, heads=heads, concat=False, dropout=DROPOUT_RATE))
        else:
            # 如果只有一层，直接映射到输出维度
            self.conv_layers[0] = GATConv(input_dim, output_dim, heads=heads, concat=False, dropout=DROPOUT_RATE)
        
        # Dropout层
        self.dropout = nn.Dropout(DROPOUT_RATE)
        
    def forward(self, x: torch.Tensor, edge_index: torch.Tensor, return_attention: bool = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 节点特征张量 [num_nodes, input_dim]
            edge_index: 边索引张量 [2, num_edges]
            return_attention: 是否返回注意力权重，如果为None则使用初始化时的设置
            
        Returns:
            如果return_attention为True: (空间嵌入张量, 注意力权重字典)
            否则: 空间嵌入张量 [num_nodes, output_dim]
        """
        if return_attention is None:
            return_attention = self.return_attention
            
        attention_weights = {} if return_attention else None
        
        # 前向传播通过所有GNN层
        for i, conv in enumerate(self.conv_layers):
            if return_attention:
                x, attention = conv(x, edge_index, return_attention_weights=True)
                attention_weights[f'layer_{i}'] = attention
            else:
                x = conv(x, edge_index)
            
            # 除了最后一层，都应用批归一化、激活函数和dropout
            if i < self.num_layers - 1:
                if i < len(self.batch_norms):
                    x = self.batch_norms[i](x)
                x = F.relu(x)
                x = self.dropout(x)
        
        if return_attention:
            return x, attention_weights
        else:
            return x


# ==================== 未来编码器模块 ====================
class FutureEncoder(nn.Module):
    """未来编码器模块，使用GRU处理时间序列特征"""
    
    def __init__(self, input_dim: int = TIME_SLICES, 
                 hidden_dim: int = HIDDEN_DIM, 
                 output_dim: int = FUTURE_EMBEDDING_DIM):
        """
        初始化GRU编码器
        
        Args:
            input_dim: 输入维度（时间切片数量）
            hidden_dim: GRU隐藏层维度
            output_dim: 输出嵌入维度
        """
        super(FutureEncoder, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        
        # GRU层，处理时间序列
        self.gru = nn.GRU(
            input_size=1,           # 每个时间步的输入大小
            hidden_size=hidden_dim,
            num_layers=2,
            batch_first=True,
            dropout=DROPOUT_RATE
        )
        
        # 线性层，将GRU输出映射到目标维度
        self.linear = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(DROPOUT_RATE),
            nn.Linear(hidden_dim, output_dim)
        )
        
    def forward(self, future_vectors: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            future_vectors: 未来竞争向量 [batch_size*4, time_slices]
            
        Returns:
            未来嵌入向量 [batch_size*4, output_dim]
        """
        batch_size, seq_len = future_vectors.shape
        
        # 重塑为GRU期望的输入格式 [batch_size, seq_len, 1]
        x = future_vectors.unsqueeze(-1)  # [batch_size*4, time_slices, 1]
        
        # 通过GRU
        gru_output, hidden = self.gru(x)  # gru_output: [batch_size*4, time_slices, hidden_dim]
        
        # 使用最后一个时间步的输出
        last_output = gru_output[:, -1, :]  # [batch_size*4, hidden_dim]
        
        # 通过线性层映射到目标维度
        future_embedding = self.linear(last_output)  # [batch_size*4, output_dim]
        
        return future_embedding


# ==================== 聚合层 ====================
class BiLSTMAggregationLayer(nn.Module):
    """双向LSTM聚合层，使用双向LSTM序列处理聚合路径上所有链路的时空状态"""
    
    def __init__(self, input_dim: int, hidden_dim: int = LSTM_HIDDEN_DIM, num_layers: int = LSTM_NUM_LAYERS):
        """
        初始化双向LSTM聚合层
        
        Args:
            input_dim: 输入嵌入维度（空间+未来嵌入的拼接）
            hidden_dim: LSTM隐藏层维度
            num_layers: LSTM层数
        """
        super(BiLSTMAggregationLayer, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        # 双向LSTM层
        self.bilstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=DROPOUT_RATE if num_layers > 1 else 0,
            bidirectional=True
        )
        
        # 线性投影层，将双向LSTM输出映射回原始维度
        # 双向LSTM输出维度是 hidden_dim * 2
        self.output_projection = nn.Linear(hidden_dim * 2, input_dim)
        
        # Dropout层
        self.dropout = nn.Dropout(DROPOUT_RATE)
        
    def forward(self, link_embeddings: torch.Tensor) -> torch.Tensor:
        """
        前向传播 - 双向LSTM序列处理
        
        Args:
            link_embeddings: 链路嵌入张量 [batch_size, 4, embedding_dim]
            
        Returns:
            聚合后的流嵌入 [batch_size, embedding_dim]
        """
        batch_size, seq_len, embedding_dim = link_embeddings.shape
        
        # 通过双向LSTM处理序列
        # lstm_output: [batch_size, seq_len, hidden_dim * 2]
        # hidden: ([num_layers * 2, batch_size, hidden_dim], [num_layers * 2, batch_size, hidden_dim])
        lstm_output, (final_hidden, final_cell) = self.bilstm(link_embeddings)
        
        # 提取最终隐藏状态：前向和后向的最后一层
        # final_hidden: [num_layers * 2, batch_size, hidden_dim]
        # 前向最后一层: final_hidden[-2, :, :]
        # 后向最后一层: final_hidden[-1, :, :]
        forward_final = final_hidden[-2, :, :]  # [batch_size, hidden_dim]
        backward_final = final_hidden[-1, :, :] # [batch_size, hidden_dim]
        
        # 拼接前向和后向的最终隐藏状态
        combined_final = torch.cat([forward_final, backward_final], dim=1)  # [batch_size, hidden_dim * 2]
        
        # 应用dropout
        combined_final = self.dropout(combined_final)
        
        # 线性投影回原始嵌入维度
        aggregated_embedding = self.output_projection(combined_final)  # [batch_size, embedding_dim]
        
        return aggregated_embedding


# ==================== 瓶颈感知聚合层 ====================
class BottleneckAwarePathAggregation(nn.Module):
    """瓶颈感知的路径聚合层"""

    def __init__(self, input_dim, hidden_dim):
        super().__init__()
        self.lstm = nn.LSTM(input_dim, hidden_dim, batch_first=True, bidirectional=True)

        # 瓶颈注意力机制
        self.bottleneck_attention = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid()
        )

        # 最终聚合
        self.final_aggregation = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU()
        )

    def forward(self, path_embeddings, bottleneck_indicators):
        # path_embeddings: [B, P, D]
        # bottleneck_indicators: [B, P, 1] (从空间特征中提取的瓶颈标识)

        # LSTM处理路径序列
        lstm_out, _ = self.lstm(path_embeddings)  # [B, P, hidden_dim*2]

        # 计算瓶颈注意力权重
        attention_weights = self.bottleneck_attention(lstm_out)  # [B, P, 1]

        # 瓶颈链路获得额外权重
        enhanced_weights = attention_weights * (1.0 + bottleneck_indicators * 3.0)  # 瓶颈权重增强
        enhanced_weights = F.softmax(enhanced_weights, dim=1)

        # 加权聚合
        aggregated = torch.sum(lstm_out * enhanced_weights, dim=1)  # [B, hidden_dim*2]

        return self.final_aggregation(aggregated)


# ==================== 交互式特征融合层 ====================
class InteractiveFeatureFusion(nn.Module):
    """交互式特征融合层"""

    def __init__(self, spatial_dim, flow_dim, competition_dim, hidden_dim):
        super().__init__()

        # 特征交互门控
        self.spatial_gate = nn.Sequential(
            nn.Linear(flow_dim, spatial_dim),
            nn.Sigmoid()
        )

        self.flow_gate = nn.Sequential(
            nn.Linear(spatial_dim + competition_dim, flow_dim),
            nn.Sigmoid()
        )

        # 融合网络
        self.fusion = nn.Sequential(
            nn.Linear(spatial_dim + flow_dim + competition_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1)
        )

    def forward(self, spatial_features, flow_features, competition_features):
        # 特征交互：流特征调制空间特征的重要性
        spatial_gated = spatial_features * self.spatial_gate(flow_features)

        # 网络状态调制流特征的重要性
        network_context = torch.cat([spatial_features, competition_features], dim=-1)
        flow_gated = flow_features * self.flow_gate(network_context)

        # 融合所有特征
        fused_features = torch.cat([spatial_gated, flow_gated, competition_features], dim=-1)
        return self.fusion(fused_features)


# ==================== 主模型类 ====================
class STGNNModel(nn.Module):
    """时空图神经网络主模型"""
    
    def __init__(self, clos_topology: Clos, **hyperparams):
        """
        初始化所有子模块
        
        Args:
            clos_topology: Clos网络拓扑
            **hyperparams: 超参数字典
        """
        super(STGNNModel, self).__init__()
        
        # 提取超参数
        self.future_window = hyperparams.get('future_window', FUTURE_WINDOW)
        self.time_slices = hyperparams.get('time_slices', TIME_SLICES)
        self.spatial_dim = hyperparams.get('spatial_embedding_dim', SPATIAL_EMBEDDING_DIM)
        self.future_dim = hyperparams.get('future_embedding_dim', FUTURE_EMBEDDING_DIM)
        self.hidden_dim = hyperparams.get('hidden_dim', HIDDEN_DIM)
        
        # 构建核心组件
        self.link_graph_builder = LinkGraphBuilder(clos_topology)
        self.feature_engineer = FeatureEngineer(
            self.link_graph_builder, 
            self.future_window, 
            self.time_slices
        )
        
        # 构建神经网络模块
        # ✅ 更新空间特征维度为4（可用带宽 + 竞争数量 + 总负载 + 瓶颈标识）
        spatial_input_dim = 4
        self.sgnn = SGNNModule(
            input_dim=spatial_input_dim,
            hidden_dim=self.hidden_dim,
            output_dim=self.spatial_dim,
            num_layers=hyperparams.get('num_gnn_layers', NUM_GNN_LAYERS),
            heads=hyperparams.get('attention_heads', 4),  # GAT注意力头数
            return_attention=hyperparams.get('return_attention', False)  # 注意力权重返回控制
        )

        self.future_encoder = FutureEncoder(
            input_dim=self.time_slices,
            hidden_dim=self.hidden_dim,
            output_dim=self.future_dim
        )

        # 流特征维度
        flow_feature_dim = 2  # 2维流特征：流大小、开始时间

        # ✅ 更新增量冲击特征维度为6
        proactive_contention_dim = 6  # 6维增强冲击特征
        
        # ✅ 使用瓶颈感知聚合层
        combined_embedding_dim = self.spatial_dim + self.future_dim + flow_feature_dim
        self.aggregation = BottleneckAwarePathAggregation(
            input_dim=combined_embedding_dim,
            hidden_dim=LSTM_HIDDEN_DIM
        )

        # ✅ 使用交互式特征融合
        self.feature_fusion = InteractiveFeatureFusion(
            spatial_dim=LSTM_HIDDEN_DIM,  # 聚合后的空间维度
            flow_dim=flow_feature_dim,
            competition_dim=proactive_contention_dim,
            hidden_dim=self.hidden_dim
        )
        
    def forward(self, batch_data: Dict, return_attention: bool = False) -> torch.Tensor:
        """
        【已修改】主前向传播函数。
        新逻辑：在批次内循环，为每个流独立计算空间特征。
        
        Args:
            batch_data: 批次数据字典
            return_attention: 是否返回注意力权重
            
        Returns:
            如果return_attention为True: (预测结果, 注意力权重字典)
            否则: 预测结果张量
        """
        flows = batch_data['flows']
        all_flows = batch_data['all_flows']
        batch_size = len(flows)
        # 动态获取设备，与输入张量保持一致
        device = next(self.parameters()).device
        edge_index = self.link_graph_builder.edge_index.to(device)

        # === 为批次中的每个样本独立准备特征 ===
        batch_path_spatial_embeddings = []
        batch_future_features = []
        batch_flow_features = []
        batch_proactive_features = []
        batch_bottleneck_indicators = []  # ✅ 新增瓶颈指示器
        batch_attention_weights = [] if return_attention else None

        # 在批次内循环，计算每个流的精确特征
        for flow in flows:
            current_time = flow['inputs']['start_time']

            # 1. ✅ 计算瓶颈感知的空间特征（4维）
            spatial_features = self.feature_engineer.compute_current_spatial_features(current_time, flow, all_flows).to(device)
            # 通过S-GNN得到全局空间嵌入
            if return_attention:
                global_spatial_embeddings, attention_weights = self.sgnn(spatial_features, edge_index, return_attention=True)
                batch_attention_weights.append(attention_weights)
            else:
                global_spatial_embeddings = self.sgnn(spatial_features, edge_index)

            # 提取路径相关的空间嵌入
            path = flow['inputs']['path']
            try:
                path_links = self.link_graph_builder.get_path_link_indices(path)
                # 如果路径无效或太短，跳过这个样本
                if not path_links: continue

                # 如果返回了注意力权重，global_spatial_embeddings是元组的第一个元素
                if isinstance(global_spatial_embeddings, tuple):
                    embeddings = global_spatial_embeddings[0]
                else:
                    embeddings = global_spatial_embeddings

                path_spatial = embeddings[path_links]
                batch_path_spatial_embeddings.append(path_spatial)

                # ✅ 提取瓶颈指示器（空间特征的第4维）
                bottleneck_indicators = spatial_features[path_links, 3:4]  # [P, 1]
                batch_bottleneck_indicators.append(bottleneck_indicators)

            except ValueError:
                # 如果路径查找失败，跳过这个坏样本
                continue

            # 2. 计算未来竞争特征
            future_features = self.feature_engineer.compute_future_temporal_features(path_links, current_time, all_flows).to(device)
            batch_future_features.append(future_features)

            # 3. 提取流自身特征
            flow_features = self.feature_engineer.extract_flow_features(flow, all_flows).to(device)
            batch_flow_features.append(flow_features)

            # 4. ✅ 计算增强的冲击特征（6维）
            proactive_features = self.feature_engineer.compute_proactive_contention_features(flow, all_flows).to(device)
            batch_proactive_features.append(proactive_features)
            

        # 如果批次中所有样本都无效，则返回空结果
        if not batch_path_spatial_embeddings:
            return torch.empty(0, 1).to(device)

        # === 将特征列表转换为批次张量 ===
        path_spatial_embeddings = torch.stack(batch_path_spatial_embeddings)   # [B, P, spatial_dim]
        future_features_tensor = torch.stack(batch_future_features)       # [B, P, time_slices]
        flow_features_tensor = torch.stack(batch_flow_features)           # [B, flow_feature_dim]
        proactive_features_tensor = torch.stack(batch_proactive_features) # [B, proactive_contention_dim]
        bottleneck_indicators_tensor = torch.stack(batch_bottleneck_indicators) # [B, P, 1]


        # === 通过模型进行批次处理 ===
        B, P, _ = future_features_tensor.shape # B: batch_size, P: path_length (4)

        # 4. 处理未来特征
        future_features_flat = future_features_tensor.view(B * P, self.time_slices)
        future_embeddings_flat = self.future_encoder(future_features_flat)
        path_future_embeddings = future_embeddings_flat.view(B, P, self.future_dim)

        # 5. 拼接时空嵌入
        combined_embeddings = torch.cat([path_spatial_embeddings, path_future_embeddings], dim=-1)

        # 5.5. 流特征广播机制 - 让每个链路都感知目标流的身份
        # 获取维度信息
        B, P, _ = combined_embeddings.shape  # B: batch_size, P: path_length
        _, F_DIM = flow_features_tensor.shape  # F_DIM: flow_feature_dim (2)

        # 将流特征从[B, F_DIM]广播为[B, P, F_DIM]
        flow_features_expanded = flow_features_tensor.unsqueeze(1).expand(B, P, F_DIM)

        # 拼接流特征到每个链路的时空特征
        final_path_embeddings = torch.cat([combined_embeddings, flow_features_expanded], dim=-1)

        # 6. ✅ 使用瓶颈感知聚合
        aggregated_embeddings = self.aggregation(final_path_embeddings, bottleneck_indicators_tensor)

        # 7. ✅ 使用交互式特征融合
        predictions = self.feature_fusion(aggregated_embeddings, flow_features_tensor, proactive_features_tensor)

        return predictions