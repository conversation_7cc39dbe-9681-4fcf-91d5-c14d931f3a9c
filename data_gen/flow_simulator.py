import random
from collections import defaultdict
from tqdm import tqdm
import numpy as np

from clos_topo import Clos


class PathManager:
    """管理路径选择 - 简化版，基于Pod结构"""

    def __init__(self, clos, congestion_config=None):
        self.clos = clos
        self.pod_hosts = self._group_hosts_by_pod()
        self.congestion_config = congestion_config or {}
        # 初始化瓶颈链路池
        self._init_bottleneck_pools()
        
    def _group_hosts_by_pod(self):
        """将主机按Pod分组"""
        pod_hosts = {}
        hosts = self.clos.nodes['H']
        for i, host in enumerate(hosts):
            pod_id = i // 4  # 每个Pod有4个主机
            if pod_id not in pod_hosts:
                pod_hosts[pod_id] = []
            pod_hosts[pod_id].append(host)
        
        # print(f"网络包含 {len(pod_hosts)} 个Pod，每个Pod有4个主机")
        return pod_hosts
    
    def _init_bottleneck_pools(self):
        """初始化上行和下行瓶颈链路池"""
        self.uplink_bottlenecks = set()    # S->P链路集合
        self.downlink_bottlenecks = set()  # P->S链路集合
        
        # 预先收集所有可能的核心链路
        for path_sample in self._sample_cross_pod_paths(20):
            if len(path_sample) == 5:
                uplink = (path_sample[1], path_sample[2])    # S->P
                downlink = (path_sample[2], path_sample[3])  # P->S
                self.uplink_bottlenecks.add(uplink)
                self.downlink_bottlenecks.add(downlink)
    
    def _sample_cross_pod_paths(self, num_samples):
        """采样跨Pod路径以收集瓶颈链路"""
        sample_paths = []
        attempts = 0
        while len(sample_paths) < num_samples and attempts < num_samples * 3:
            source, target = self.generate_flow_pair()
            if source and target:
                path = self.get_cross_pod_path_simple(source, target)
                if path:
                    sample_paths.append(path)
            attempts += 1
        return sample_paths
    
    def get_cross_pod_path_simple(self, source, target):
        """获取跨Pod路径(不考虑拥塞控制)"""
        try:
            all_paths = list(self.clos.find_all_paths(source, target, cutoff=5))
            four_hop_paths = [p for p in all_paths if len(p) == 5]
            return random.choice(four_hop_paths) if four_hop_paths else None
        except:
            return None
    
    def get_cross_pod_path(self, source, target):
        """生成跨Pod的4跳路径，支持拥塞控制"""
        try:
            all_paths = list(self.clos.find_all_paths(source, target, cutoff=5))
            four_hop_paths = [p for p in all_paths if len(p) == 5]
            
            if not four_hop_paths:
                return None
                
            # 如果没有拥塞配置，使用原始随机选择
            if not self.congestion_config:
                return random.choice(four_hop_paths)
                
            # 应用拥塞控制策略
            return self._apply_congestion_strategy(four_hop_paths)
        except:
            return None
    
    def _apply_congestion_strategy(self, candidate_paths):
        """应用拥塞控制策略选择路径"""
        uplink_ratio = self.congestion_config.get('uplink_congestion_ratio', 0.0)
        downlink_ratio = self.congestion_config.get('downlink_congestion_ratio', 0.0)
        
        # 决定是否强制使用瓶颈链路
        force_uplink = random.random() < uplink_ratio
        force_downlink = random.random() < downlink_ratio
        
        if not force_uplink and not force_downlink:
            # 不强制拥塞，随机选择
            return random.choice(candidate_paths)
        
        # 筛选包含指定瓶颈链路的路径
        filtered_paths = []
        for path in candidate_paths:
            uplink = (path[1], path[2])    # S->P
            downlink = (path[2], path[3])  # P->S
            
            uplink_match = not force_uplink or uplink in self.uplink_bottlenecks
            downlink_match = not force_downlink or downlink in self.downlink_bottlenecks
            
            if uplink_match and downlink_match:
                filtered_paths.append(path)
        
        # 如果没有符合条件的路径，回退到随机选择
        return random.choice(filtered_paths) if filtered_paths else random.choice(candidate_paths)
    
    def get_intra_pod_path(self, source, target):
        """生成Pod内的2跳路径"""
        try:
            all_paths = list(self.clos.find_all_paths(source, target, cutoff=3))
            two_hop_paths = [p for p in all_paths if len(p) == 3]  # 2跳=3个节点
            
            if two_hop_paths:
                return random.choice(two_hop_paths)
            else:
                return None
        except:
            return None

    def generate_flow_pair(self):
        """生成一个随机的跨Pod流源目标对"""
        all_hosts = []
        for hosts in self.pod_hosts.values():
            all_hosts.extend(hosts)
        
        if len(all_hosts) < 2:
            return None, None
        
        source, target = random.sample(all_hosts, 2)
        source_pod_id = next((pid for pid, hosts in self.pod_hosts.items() if source in hosts), -1)
        target_pod_id = next((pid for pid, hosts in self.pod_hosts.items() if target in hosts), -1)

        # 确保是跨Pod
        if source_pod_id != target_pod_id:
            return source, target
        else:
            # 如果碰巧在同一个pod，重新生成一次
            return self.generate_flow_pair()


class TimeBasedFlowScheduler:
    """基于时间的流调度器 - 流水线模型版本
    采用流水线(Pipelining)/直通交换(Cut-through)模型进行仿真。
    一个流的速率由其路径上的瓶颈链路决定。
    """

    def __init__(self, topology, tasks_data=None, timestep=0.01, max_time=10.0):
        self.topology = topology
        self.timestep = timestep
        self.max_time = max_time
        self.current_time = 0.0
        
        # 任务和流状态管理
        self.tasks_data = tasks_data if tasks_data else []
        self.pending_flows = {}   # 等待开始的流 {start_time: [flow_info, ...]}
        self.active_flows = {}    # 正在传输的流 {flow_id: flow_info}
        self.completed_flows = {} # 已完成的流 {flow_id: flow_info}

        # 性能优化：增量维护边竞争计数
        self.edge_contention = defaultdict(int)

        # 仿真结果
        self.flow_completion_times = {} # 记录每个流的完成时间 {flow_id: completion_time}

        # 初始化任务
        if self.tasks_data:
            self.initialize_flows()

    def initialize_flows(self):
        """
        初始化所有流，将它们放入等待队列。
        """
        for task in self.tasks_data:
            flow_id = task['task_id']
            start_time = round(task.get('comm_start_time', 0.0), 4) # 对开始时间进行取整以分组
            
            flow_info = {
                'flow_id': flow_id,
                'path': task['paths'][0],
                'size': task['gradient_size_mb'],
                'start_time': start_time,
                'total_bytes_sent': 0.0,
                'end_time': -1.0
            }

            if start_time not in self.pending_flows:
                self.pending_flows[start_time] = []
            
            # 将流信息添加到对应开始时间的列表中
            self.pending_flows[start_time].append(flow_info)
        
        # 打印初始化信息
        total_flows = len(self.tasks_data)
        # print(f"调度器初始化完成: {total_flows} 个流被加载到等待队列。")

    def _add_flow_to_contention(self, flow_info):
        """当一个流开始时，增加其路径上所有边的计数值"""
        path = flow_info['path']
        for i in range(len(path) - 1):
            edge = (path[i], path[i+1])
            self.edge_contention[edge] += 1

    def _remove_flow_from_contention(self, flow_info):
        """当一个流结束时，减少其路径上所有边的计数值"""
        path = flow_info['path']
        for i in range(len(path) - 1):
            edge = (path[i], path[i+1])
            self.edge_contention[edge] -= 1
            if self.edge_contention[edge] == 0:
                del self.edge_contention[edge] # 保持字典干净

    def simulate_time_based(self):
        """
        运行基于时间的流水线模型仿真。
        """
        # print(f"开始流水线模型仿真，最大时间: {self.max_time}s，时间步长: {self.timestep}s")
        
        # 按时间排序等待队列的键
        pending_times = sorted(self.pending_flows.keys())
        
        pbar = tqdm(total=len(self.tasks_data), desc="Simulating Flows", leave=False)

        while self.current_time < self.max_time:
            
            # 1. 启动新到达的流
            # 检查是否有在当前时间步或之前需要开始的流
            while pending_times and self.current_time >= pending_times[0]:
                start_time = pending_times.pop(0)
                for flow_info in self.pending_flows[start_time]:
                    self.active_flows[flow_info['flow_id']] = flow_info
                    self._add_flow_to_contention(flow_info)

            if not self.active_flows:
                if not pending_times:
                    # 所有流都完成了
                    # print("所有流均已完成或启动，仿真结束。")
                    break
                else:
                    self.current_time = pending_times[0]
                    continue

            # 2. 计算每个活跃流的瓶颈带宽
            flow_rates = {}
            for flow_id, flow_info in self.active_flows.items():
                min_bw = float('inf')
                path = flow_info['path']
                for i in range(len(path) - 1):
                    edge = (path[i], path[i+1])
                    try:
                        capacity = self.topology.G[edge[0]][edge[1]]['btw']
                    except KeyError:
                        capacity = 400 # 默认带宽

                    contention = self.edge_contention[edge]
                    
                    shared_bw = capacity / contention if contention > 0 else capacity
                    if shared_bw < min_bw:
                        min_bw = shared_bw
                
                flow_rates[flow_id] = min_bw

            # 3. 根据瓶颈速率更新流的传输进度
            flows_to_complete = []
            for flow_id, flow_info in self.active_flows.items():
                rate = flow_rates[flow_id]
                bytes_sent = rate * self.timestep
                flow_info['total_bytes_sent'] += bytes_sent
                
                if flow_info['total_bytes_sent'] >= flow_info['size']:
                    flows_to_complete.append(flow_id)

            # 4. 处理已完成的流
            if flows_to_complete:
                for flow_id in flows_to_complete:
                    if flow_id in self.active_flows:
                        completed_flow = self.active_flows.pop(flow_id)
                        
                        overshoot_bytes = completed_flow['total_bytes_sent'] - completed_flow['size']
                        overshoot_time = overshoot_bytes / flow_rates[flow_id] if flow_rates[flow_id] > 0 else 0
                        completion_time = self.current_time + self.timestep - overshoot_time
                        
                        completed_flow['end_time'] = completion_time
                        self.completed_flows[flow_id] = completed_flow
                        self.flow_completion_times[flow_id] = completion_time
                        
                        self._remove_flow_from_contention(completed_flow)
                        pbar.update(1)

            # 5. 推进时间
            self.current_time += self.timestep

            if not self.active_flows and not pending_times:
                # print(f"所有流已完成，仿真在时间 {self.current_time:.3f}s 结束。")
                break
        
        pbar.close()
        # self.calculate_statistics()
        return self.get_simulation_results()

    def get_simulation_results(self):
        """
        返回仿真结果，主要为每个流的完成时间(FCT)和总延迟。
        """
        results = {}
        for flow_id, flow_info in self.completed_flows.items():
            fct = flow_info['end_time'] - flow_info['start_time']
            results[flow_id] = {
                "fct": fct,
                "start_time": flow_info['start_time'],
                "end_time": flow_info['end_time'],
                "size_mb": flow_info['size']
            }
        
        for flow_id, flow_info in self.active_flows.items():
             results[flow_id] = {
                "fct": -1, # -1 表示未完成
                "start_time": flow_info['start_time'],
                "end_time": -1,
                "size_mb": flow_info['size']
            }
        return results

    def calculate_statistics(self):
        """计算最终的统计信息"""
        total_flows = len(self.tasks_data)
        completed_count = len(self.completed_flows)
        
        print(f"\n=== 仿真统计 (最终时间: {self.current_time:.3f}s) ===")
        print(f"总流数: {total_flows}")
        print(f"已完成: {completed_count}")
        print(f"未完成: {total_flows - completed_count}")
        
        if completed_count > 0:
            completion_rate = completed_count / total_flows
            fcts = [res['fct'] for res in self.get_simulation_results().values() if res['fct'] != -1]
            if fcts:
                avg_fct = np.mean(fcts)
                print(f"完成率: {completion_rate:.2%}")
                print(f"平均流完成时间 (FCT): {avg_fct:.4f}s")
        else:
            print("没有流完成。") 